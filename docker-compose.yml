version: '3.8'

services:
  docker-lb:
    build: .
    container_name: docker-lb
    ports:
      - "60010:60010"
    volumes:
      - ./config.yaml:/root/config.yaml:ro
      - ./logs:/root/logs
    environment:
      - TZ=Asia/Shanghai
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:60010/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - docker-lb-network

  # 可选：添加Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: docker-lb-nginx
    ports:
      - "60020:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - docker-lb
    restart: unless-stopped
    networks:
      - docker-lb-network

networks:
  docker-lb-network:
    driver: bridge
