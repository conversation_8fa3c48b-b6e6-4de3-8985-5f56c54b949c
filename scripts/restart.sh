#!/bin/bash

# Docker负载均衡器重启脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 主函数
main() {
    echo "========================================"
    echo "    Docker负载均衡器重启脚本"
    echo "========================================"
    
    log_info "开始重启服务..."
    
    # 停止服务
    if [ -f "scripts/stop.sh" ]; then
        log_info "停止现有服务..."
        bash scripts/stop.sh
    else
        log_warning "stop.sh脚本不存在，尝试手动停止..."
        pkill -f "docker-lb" || true
        sleep 3
    fi
    
    # 等待一段时间确保服务完全停止
    sleep 2
    
    # 启动服务
    if [ -f "scripts/start.sh" ]; then
        log_info "启动服务..."
        bash scripts/start.sh
    else
        log_error "start.sh脚本不存在"
        exit 1
    fi
    
    echo "========================================"
    log_success "重启完成！"
    echo "========================================"
}

# 执行主函数
main "$@"
