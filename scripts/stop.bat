@echo off
setlocal enabledelayedexpansion

:: Docker负载均衡器停止脚本 (Windows版本)

echo ========================================
echo     Docker负载均衡器停止脚本
echo ========================================

echo [INFO] 正在停止Docker负载均衡器...

:: 查找并停止进程
tasklist | findstr "docker-lb.exe" >nul
if %errorlevel% equ 0 (
    echo [INFO] 找到运行中的进程，正在停止...
    taskkill /f /im docker-lb.exe >nul 2>nul
    if %errorlevel% equ 0 (
        echo [SUCCESS] Docker负载均衡器已停止
    ) else (
        echo [ERROR] 无法停止进程，请手动检查
        pause
        exit /b 1
    )
) else (
    echo [WARNING] 没有找到运行中的Docker负载均衡器进程
)

:: 清理临时文件
echo [INFO] 清理临时文件...
if exist "docker-lb.pid" del "docker-lb.pid"

echo ========================================
echo [SUCCESS] 停止完成！
echo ========================================

pause
