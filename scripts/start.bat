@echo off
setlocal enabledelayedexpansion

:: Docker负载均衡器启动脚本 (Windows版本)

echo ========================================
echo     Docker负载均衡器启动脚本
echo ========================================

:: 检查Go是否安装
where go >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Go未安装，请先安装Go 1.21或更高版本
    pause
    exit /b 1
)

:: 检查Node.js是否安装
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Node.js未安装，请先安装Node.js 18或更高版本
    pause
    exit /b 1
)

:: 创建必要目录
echo [INFO] 创建必要目录...
if not exist "bin" mkdir bin
if not exist "logs" mkdir logs
if not exist "ssl" mkdir ssl

:: 检查配置文件
echo [INFO] 检查配置文件...
if not exist "config.yaml" (
    echo [WARNING] config.yaml不存在，请确保配置文件存在
)

:: 构建后端
echo [INFO] 构建Go后端...
go mod tidy
if %errorlevel% neq 0 (
    echo [ERROR] Go依赖安装失败
    pause
    exit /b 1
)

go build -o bin\docker-lb.exe cmd\main.go
if %errorlevel% neq 0 (
    echo [ERROR] 后端构建失败
    pause
    exit /b 1
)
echo [SUCCESS] 后端构建完成

:: 构建前端
echo [INFO] 构建Vue前端...
cd web
call npm install
if %errorlevel% neq 0 (
    echo [ERROR] 前端依赖安装失败
    cd ..
    pause
    exit /b 1
)

call npm run build
if %errorlevel% neq 0 (
    echo [ERROR] 前端构建失败
    cd ..
    pause
    exit /b 1
)
cd ..
echo [SUCCESS] 前端构建完成

:: 检查端口是否被占用
echo [INFO] 检查端口60010...
netstat -an | findstr ":60010" >nul
if %errorlevel% equ 0 (
    echo [WARNING] 端口60010已被占用，请手动停止占用进程
)

:: 启动服务
echo [INFO] 启动Docker负载均衡器...
start /b bin\docker-lb.exe > logs\docker-lb.log 2>&1

:: 等待服务启动
echo [INFO] 等待服务启动...
timeout /t 5 /nobreak >nul

:: 检查服务状态
curl -s http://localhost:60010/api/v1/health >nul 2>nul
if %errorlevel% equ 0 (
    echo [SUCCESS] 服务启动成功！
    echo [INFO] Web界面: http://localhost:60010
    echo [INFO] API接口: http://localhost:60010/api/v1/health
) else (
    echo [ERROR] 服务启动失败，请检查日志: logs\docker-lb.log
    pause
    exit /b 1
)

echo ========================================
echo [SUCCESS] 启动完成！
echo ========================================
echo.
echo 服务状态:
echo   - 后端服务: http://localhost:60010
echo   - 前端界面: http://localhost:60020
echo   - 日志文件: logs\docker-lb.log
echo.
echo 常用命令:
echo   - 查看日志: type logs\docker-lb.log
echo   - 停止服务: scripts\stop.bat
echo   - 重启服务: scripts\restart.bat
echo.

pause
