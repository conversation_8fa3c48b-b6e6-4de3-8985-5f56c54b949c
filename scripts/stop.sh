#!/bin/bash

# Docker负载均衡器停止脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止服务
stop_service() {
    log_info "正在停止Docker负载均衡器..."
    
    # 查找并停止进程
    PIDS=$(pgrep -f "docker-lb" || true)
    
    if [ -z "$PIDS" ]; then
        log_warning "没有找到运行中的Docker负载均衡器进程"
        return 0
    fi
    
    # 优雅停止
    log_info "发送SIGTERM信号..."
    echo "$PIDS" | xargs kill -TERM 2>/dev/null || true
    
    # 等待进程停止
    sleep 5
    
    # 检查是否还有进程运行
    REMAINING_PIDS=$(pgrep -f "docker-lb" || true)
    
    if [ -n "$REMAINING_PIDS" ]; then
        log_warning "进程仍在运行，强制停止..."
        echo "$REMAINING_PIDS" | xargs kill -KILL 2>/dev/null || true
        sleep 2
    fi
    
    # 最终检查
    FINAL_PIDS=$(pgrep -f "docker-lb" || true)
    
    if [ -z "$FINAL_PIDS" ]; then
        log_success "Docker负载均衡器已停止"
    else
        log_error "无法停止所有进程，请手动检查"
        exit 1
    fi
}

# 清理资源
cleanup() {
    log_info "清理临时文件..."
    
    # 清理PID文件
    rm -f docker-lb.pid
    
    # 清理临时日志
    # rm -f logs/docker-lb.log.tmp
    
    log_success "清理完成"
}

# 主函数
main() {
    echo "========================================"
    echo "    Docker负载均衡器停止脚本"
    echo "========================================"
    
    stop_service
    cleanup
    
    echo "========================================"
    log_success "停止完成！"
    echo "========================================"
}

# 执行主函数
main "$@"
