import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  server: {
    host: "0.0.0.0",
    port: 60020,
    proxy: {
      '/api': {
        target: 'http://0.0.0.0:60010',
        changeOrigin: true
      },
      '/ws': {
        target: 'ws://0.0.0.0:60010',
        ws: true
      }
    }
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets'
  }
})
