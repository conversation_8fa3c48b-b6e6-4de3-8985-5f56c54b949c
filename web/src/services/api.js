import axios from 'axios'

const apiClient = axios.create({
  baseURL: '/api/v1',
  timeout: 10000
})

// 请求拦截器
apiClient.interceptors.request.use(
  config => {
    // 添加认证token
    const token = localStorage.getItem('docker_lb_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    console.error('API Error:', error)

    // 处理认证错误
    if (error.response?.status === 401) {
      // 清除本地存储的认证信息
      localStorage.removeItem('docker_lb_token')
      localStorage.removeItem('docker_lb_user')

      // 如果不在登录页面，跳转到登录页面
      if (window.location.pathname !== '/login') {
        window.location.href = '/login'
      }
    }

    return Promise.reject(error)
  }
)

export const apiService = {
  // 获取健康状态
  getHealth() {
    return apiClient.get('/health')
  },

  // 获取镜像源列表
  getRegistries() {
    return apiClient.get('/registries')
  },

  // 获取统计信息
  getStats() {
    return apiClient.get('/stats')
  },

  // 启用镜像源
  enableRegistry(name) {
    return apiClient.post(`/registries/${name}/enable`)
  },

  // 禁用镜像源
  disableRegistry(name) {
    return apiClient.post(`/registries/${name}/disable`)
  },

  // 流量统计相关接口
  getHourlyTrafficStats(hours = 24) {
    return apiClient.get(`/monitoring/traffic/hourly?hours=${hours}`)
  },

  getDailyTrafficSummary(days = 7) {
    return apiClient.get(`/monitoring/traffic/daily?days=${days}`)
  },

  getRegistryTrafficStats(hours = 24) {
    return apiClient.get(`/monitoring/traffic/registry?hours=${hours}`)
  },

  getTopTrafficIPs(limit = 20) {
    return apiClient.get(`/monitoring/ips/traffic?limit=${limit}`)
  },

  // 镜像源配置管理
  getRegistryConfigs() {
    return apiClient.get('/registry-configs')
  },

  createRegistryConfig(config) {
    return apiClient.post('/registry-configs', config)
  },

  updateRegistryConfig(id, config) {
    return apiClient.put(`/registry-configs/${id}`, config)
  },

  deleteRegistryConfig(id) {
    return apiClient.delete(`/registry-configs/${id}`)
  }
}

// 导出 apiClient 作为默认导出，以便 auth.js 可以使用 api.post 等方法
export default apiClient
// apiService 已经在上面通过 export const 导出了，不需要重复导出
