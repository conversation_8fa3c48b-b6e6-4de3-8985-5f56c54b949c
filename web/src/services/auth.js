import api from './api'

const TOKEN_KEY = 'docker_lb_token'
const USER_KEY = 'docker_lb_user'

class AuthService {
  constructor() {
    this.token = localStorage.getItem(TOKEN_KEY)
    this.user = JSON.parse(localStorage.getItem(USER_KEY) || 'null')
  }

  // 登录
  async login(username, password) {
    const response = await api.post('/auth/login', {
      username,
      password
    })
    return response
  }

  // 退出登录
  logout() {
    this.token = null
    this.user = null
    localStorage.removeItem(TOKEN_KEY)
    localStorage.removeItem(USER_KEY)
  }

  // 设置token
  setToken(token) {
    this.token = token
    localStorage.setItem(TOKEN_KEY, token)
  }

  // 获取token
  getToken() {
    return this.token
  }

  // 设置用户信息
  setUserInfo(user) {
    this.user = user
    localStorage.setItem(USER_KEY, JSON.stringify(user))
  }

  // 获取用户信息
  getUserInfo() {
    return this.user
  }

  // 检查是否已登录
  isAuthenticated() {
    if (!this.token || !this.user) {
      return false
    }

    // 检查token是否过期
    if (this.user.expiresAt) {
      const expiresAt = new Date(this.user.expiresAt)
      if (expiresAt <= new Date()) {
        this.logout()
        return false
      }
    }

    return true
  }

  // 刷新token
  async refreshToken() {
    try {
      const response = await api.post('/auth/refresh')
      this.setToken(response.token)
      this.setUserInfo({
        username: response.username,
        expiresAt: response.expires_at
      })
      return response
    } catch (error) {
      this.logout()
      throw error
    }
  }

  // 获取用户信息
  async fetchUserInfo() {
    try {
      const response = await api.get('/auth/user')
      return response
    } catch (error) {
      this.logout()
      throw error
    }
  }

  // 修改密码
  async changePassword(oldPassword, newPassword) {
    const response = await api.post('/auth/change-password', {
      old_password: oldPassword,
      new_password: newPassword
    })
    return response
  }
}

export const authService = new AuthService()
export default authService
