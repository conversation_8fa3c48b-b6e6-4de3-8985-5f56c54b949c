<template>
  <div class="statistics">
    <!-- 统计概览 -->
    <el-row :gutter="20" class="stats-overview">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ totalRequests }}</div>
            <div class="stat-label">总请求数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ successRequests }}</div>
            <div class="stat-label">成功请求</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ failedRequests }}</div>
            <div class="stat-label">失败请求</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ successRate }}%</div>
            <div class="stat-label">成功率</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>请求分布</span>
          </template>
          <div class="chart-container">
            <canvas ref="requestChart" width="400" height="300"></canvas>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>响应时间分布</span>
          </template>
          <div class="chart-container">
            <canvas ref="responseChart" width="400" height="300"></canvas>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细统计表格 -->
    <el-row class="table-section">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>镜像源详细统计</span>
              <el-button type="primary" @click="refreshData">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          
          <el-table :data="registries" style="width: 100%" :empty-text="'暂无数据'" :max-height="600">
            <el-table-column prop="name" label="镜像源" width="200" />
            
            <el-table-column label="请求统计" align="center">
              <el-table-column prop="total_requests" label="总数" width="100" align="center" />
              <el-table-column prop="success_requests" label="成功" width="100" align="center">
                <template #default="scope">
                  <span style="color: #67C23A">{{ scope.row.success_requests }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="failed_requests" label="失败" width="100" align="center">
                <template #default="scope">
                  <span style="color: #F56C6C">{{ scope.row.failed_requests }}</span>
                </template>
              </el-table-column>
              <el-table-column label="成功率" width="100" align="center">
                <template #default="scope">
                  <span>{{ getSuccessRate(scope.row) }}%</span>
                </template>
              </el-table-column>
            </el-table-column>
            
            <el-table-column prop="health_check_response" label="健康检查延时" width="150" align="center">
              <template #default="scope">
                <el-tag :type="getResponseTimeType(scope.row.health_check_response)" v-if="scope.row.health_check_response > 0">
                  {{ scope.row.health_check_response }}ms
                </el-tag>
                <span v-else style="color: #c0c4cc;">-</span>
              </template>
            </el-table-column>
            
            <el-table-column prop="weight" label="权重" width="80" align="center" />
            
            <el-table-column prop="healthy" label="状态" width="100" align="center">
              <template #default="scope">
                <el-tag :type="scope.row.healthy ? 'success' : 'danger'">
                  {{ scope.row.healthy ? '健康' : '异常' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { Refresh } from '@element-plus/icons-vue'
import { Chart, registerables } from 'chart.js'
import { apiService as api } from '../services/api'
import { useWebSocket } from '../composables/useWebSocket'

Chart.register(...registerables)

export default {
  name: 'Statistics',
  components: {
    Refresh
  },
  setup() {
    const registries = ref([])
    const totalRequests = ref(0)
    const successRequests = ref(0)
    const failedRequests = ref(0)
    const successRate = ref(0)
    
    const requestChart = ref(null)
    const responseChart = ref(null)
    let requestChartInstance = null
    let responseChartInstance = null

    const { connect, disconnect } = useWebSocket()

    const loadData = async () => {
      try {
        const [registriesData, statsData] = await Promise.all([
          api.getRegistries(),
          api.getStats()
        ])
        
        registries.value = registriesData.registries || []
        totalRequests.value = statsData.total_requests || 0
        successRequests.value = statsData.success_requests || 0
        failedRequests.value = statsData.failed_requests || 0
        successRate.value = Math.round(statsData.success_rate || 0)
        
        updateCharts()
      } catch (error) {
        console.error('Failed to load statistics:', error)
      }
    }

    const updateCharts = async () => {
      await nextTick()
      updateRequestChart()
      updateResponseChart()
    }

    const updateRequestChart = () => {
      if (requestChartInstance) {
        requestChartInstance.destroy()
      }

      if (requestChart.value && registries.value.length > 0) {
        const ctx = requestChart.value.getContext('2d')
        const labels = registries.value.map(r => r.name)
        const successData = registries.value.map(r => r.success_requests)
        const failedData = registries.value.map(r => r.failed_requests)

        requestChartInstance = new Chart(ctx, {
          type: 'bar',
          data: {
            labels,
            datasets: [
              {
                label: '成功请求',
                data: successData,
                backgroundColor: '#67C23A',
                borderColor: '#67C23A',
                borderWidth: 1
              },
              {
                label: '失败请求',
                data: failedData,
                backgroundColor: '#F56C6C',
                borderColor: '#F56C6C',
                borderWidth: 1
              }
            ]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              y: {
                beginAtZero: true
              }
            }
          }
        })
      }
    }

    const updateResponseChart = () => {
      if (responseChartInstance) {
        responseChartInstance.destroy()
      }

      if (responseChart.value && registries.value.length > 0) {
        const ctx = responseChart.value.getContext('2d')
        const labels = registries.value.map(r => r.name)
        const responseData = registries.value.map(r => r.average_response)

        responseChartInstance = new Chart(ctx, {
          type: 'line',
          data: {
            labels,
            datasets: [{
              label: '平均响应时间 (ms)',
              data: responseData,
              borderColor: '#409EFF',
              backgroundColor: 'rgba(64, 158, 255, 0.1)',
              borderWidth: 2,
              fill: true,
              tension: 0.4
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              y: {
                beginAtZero: true
              }
            }
          }
        })
      }
    }

    const refreshData = () => {
      loadData()
    }

    const getSuccessRate = (registry) => {
      if (registry.total_requests === 0) return 0
      return Math.round((registry.success_requests / registry.total_requests) * 100)
    }

    const getResponseTimeType = (time) => {
      if (time < 500) return 'success'
      if (time < 1000) return 'warning'
      return 'danger'
    }

    onMounted(() => {
      loadData()
      
      // 连接WebSocket接收实时更新
      connect((data) => {
        if (data.type === 'stats_update' && data.registries) {
          registries.value = data.registries
          
          // 重新计算统计数据
          totalRequests.value = registries.value.reduce((sum, r) => sum + r.total_requests, 0)
          successRequests.value = registries.value.reduce((sum, r) => sum + r.success_requests, 0)
          failedRequests.value = registries.value.reduce((sum, r) => sum + r.failed_requests, 0)
          
          if (totalRequests.value > 0) {
            successRate.value = Math.round((successRequests.value / totalRequests.value) * 100)
          }
          
          updateCharts()
        }
      })
    })

    onUnmounted(() => {
      if (requestChartInstance) {
        requestChartInstance.destroy()
      }
      if (responseChartInstance) {
        responseChartInstance.destroy()
      }
      disconnect()
    })

    return {
      registries,
      totalRequests,
      successRequests,
      failedRequests,
      successRate,
      requestChart,
      responseChart,
      refreshData,
      getSuccessRate,
      getResponseTimeType
    }
  }
}
</script>

<style scoped>
.statistics {
  padding: 0;
}

.stats-overview {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  padding: 20px 0;
}

.stat-content {
  padding: 10px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 10px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
  position: relative;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
