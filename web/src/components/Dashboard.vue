<template>
  <div class="dashboard">
    <!-- 概览卡片 -->
    <el-row :gutter="20" class="overview-cards">
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon healthy">
              <el-icon size="24"><CircleCheck /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">健康节点</div>
              <div class="card-value">{{ healthyCount }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon total">
              <el-icon size="24"><Setting /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">总节点</div>
              <div class="card-value">{{ totalCount }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon requests">
              <el-icon size="24"><DataLine /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">总请求数</div>
              <div class="card-value">{{ totalRequests }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon success-rate">
              <el-icon size="24"><TrendCharts /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">成功率</div>
              <div class="card-value">{{ successRate }}%</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 实时状态 -->
    <el-row :gutter="20" class="status-section">
      <el-col :span="16">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>镜像源实时状态</span>
              <el-button type="primary" size="small" @click="refreshData">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          
          <div class="registry-status">
            <div 
              v-for="registry in registries" 
              :key="registry.name"
              class="registry-item"
              :class="{ 'healthy': registry.healthy, 'unhealthy': !registry.healthy }"
            >
              <div class="registry-info">
                <div class="registry-name">{{ registry.name }}</div>
                <div class="registry-url">{{ registry.url }}</div>
              </div>
              <div class="registry-metrics">
                <div class="metric">
                  <span class="metric-label">请求数:</span>
                  <span class="metric-value">{{ registry.total_requests }}</span>
                </div>
                <div class="metric">
                  <span class="metric-label">响应时间:</span>
                  <span class="metric-value">{{ registry.average_response }}ms</span>
                </div>
              </div>
              <div class="registry-status-indicator">
                <el-tag :type="registry.healthy ? 'success' : 'danger'">
                  {{ registry.healthy ? '健康' : '异常' }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card>
          <template #header>
            <span>健康状态分布</span>
          </template>
          <div class="health-chart">
            <canvas ref="healthChart" width="300" height="200"></canvas>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { CircleCheck, Setting, DataLine, TrendCharts, Refresh } from '@element-plus/icons-vue'
import { Chart, registerables } from 'chart.js'
import { apiService as api } from '../services/api'
import { useWebSocket } from '../composables/useWebSocket'

Chart.register(...registerables)

export default {
  name: 'Dashboard',
  components: {
    CircleCheck,
    Setting,
    DataLine,
    TrendCharts,
    Refresh
  },
  setup() {
    const registries = ref([])
    const healthyCount = ref(0)
    const totalCount = ref(0)
    const totalRequests = ref(0)
    const successRate = ref(0)
    const healthChart = ref(null)
    let chartInstance = null

    const { connect, disconnect } = useWebSocket()

    const loadData = async () => {
      try {
        const [registriesData, statsData] = await Promise.all([
          api.getRegistries(),
          api.getStats()
        ])
        
        registries.value = registriesData.registries || []
        totalRequests.value = statsData.total_requests || 0
        successRate.value = Math.round(statsData.success_rate || 0)
        
        updateCounts()
        updateChart()
      } catch (error) {
        console.error('Failed to load data:', error)
      }
    }

    const updateCounts = () => {
      totalCount.value = registries.value.length
      healthyCount.value = registries.value.filter(r => r.healthy).length
    }

    const updateChart = async () => {
      await nextTick()
      
      if (chartInstance) {
        chartInstance.destroy()
      }

      if (healthChart.value) {
        const ctx = healthChart.value.getContext('2d')
        chartInstance = new Chart(ctx, {
          type: 'doughnut',
          data: {
            labels: ['健康', '异常'],
            datasets: [{
              data: [healthyCount.value, totalCount.value - healthyCount.value],
              backgroundColor: ['#67C23A', '#F56C6C'],
              borderWidth: 0
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'bottom'
              }
            }
          }
        })
      }
    }

    const refreshData = () => {
      loadData()
    }

    onMounted(() => {
      loadData()
      
      // 连接WebSocket接收实时更新
      connect((data) => {
        if (data.type === 'stats_update' && data.registries) {
          registries.value = data.registries
          updateCounts()
          updateChart()
        }
      })
    })

    onUnmounted(() => {
      if (chartInstance) {
        chartInstance.destroy()
      }
      disconnect()
    })

    return {
      registries,
      healthyCount,
      totalCount,
      totalRequests,
      successRate,
      healthChart,
      refreshData
    }
  }
}
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.overview-cards {
  margin-bottom: 20px;
}

.overview-card {
  height: 100px;
}

.card-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.card-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: white;
}

.card-icon.healthy {
  background-color: #67C23A;
}

.card-icon.total {
  background-color: #409EFF;
}

.card-icon.requests {
  background-color: #E6A23C;
}

.card-icon.success-rate {
  background-color: #909399;
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 5px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.registry-status {
  max-height: 400px;
  overflow-y: auto;
}

.registry-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  margin-bottom: 10px;
  transition: all 0.3s;
}

.registry-item.healthy {
  border-left: 4px solid #67C23A;
}

.registry-item.unhealthy {
  border-left: 4px solid #F56C6C;
}

.registry-info {
  flex: 1;
}

.registry-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.registry-url {
  font-size: 12px;
  color: #909399;
}

.registry-metrics {
  display: flex;
  gap: 20px;
  margin-right: 20px;
}

.metric {
  text-align: center;
}

.metric-label {
  display: block;
  font-size: 12px;
  color: #909399;
}

.metric-value {
  display: block;
  font-weight: bold;
  margin-top: 2px;
}

.health-chart {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
