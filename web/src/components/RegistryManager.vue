<template>
  <div class="registry-manager">
    <div class="header">
      <h2>镜像源管理</h2>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          添加镜像源
        </el-button>
      </div>
    </div>

    <!-- 镜像源列表 -->
    <el-table :data="registries" v-loading="loading" stripe class="registry-table" empty-text="暂无镜像源数据">
      <!-- 序号 -->
      <el-table-column type="index" label="序号" width="60" align="center" />

      <el-table-column prop="name" label="名称" width="160" show-overflow-tooltip>
        <template #default="scope">
          <div style="font-weight: 500; color: #303133;">
            {{ scope.row.name }}
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="url" label="URL" min-width="220" show-overflow-tooltip>
        <template #default="scope">
          <el-link :href="scope.row.url" target="_blank" type="primary" style="font-size: 12px;">
            {{ scope.row.url }}
          </el-link>
        </template>
      </el-table-column>

      <!-- 健康状态 -->
      <el-table-column label="健康状态" width="100" align="center">
        <template #default="scope">
          <el-tag
            :type="scope.row.healthy ? 'success' : 'danger'"
            size="small"
            effect="light"
            round
          >
            <el-icon style="margin-right: 4px;">
              <CircleCheck v-if="scope.row.healthy" />
              <CircleClose v-else />
            </el-icon>
            {{ scope.row.healthy ? '健康' : '异常' }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 启用状态 -->
      <el-table-column label="启用状态" width="100" align="center">
        <template #default="scope">
          <el-switch
            v-model="scope.row.enabled"
            @change="toggleRegistry(scope.row)"
            :loading="scope.row.switching"
            size="small"
            active-text="启用"
            inactive-text="禁用"
            inline-prompt
          />
        </template>
      </el-table-column>

      <!-- 权重 -->
      <el-table-column prop="weight" label="权重" width="80" align="center">
        <template #default="scope">
          <el-tag size="small" type="info" effect="plain">
            {{ scope.row.weight || '-' }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 超时时间 -->
      <el-table-column prop="timeout_seconds" label="超时(秒)" width="100" align="center">
        <template #default="scope">
          <span style="color: #606266;">{{ scope.row.timeout_seconds || '-' }}</span>
        </template>
      </el-table-column>

      <!-- 统计信息 -->
      <el-table-column label="请求统计" width="130" align="center">
        <template #default="scope">
          <div class="stats-info">
            <div class="stat-item">
              <span class="stat-label">总数:</span>
              <span class="stat-value">{{ scope.row.total_requests || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">成功:</span>
              <span class="stat-value success">{{ scope.row.success_requests || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">失败:</span>
              <span class="stat-value error">{{ scope.row.failed_requests || 0 }}</span>
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 响应时间 -->
      <el-table-column label="响应时间" width="100" align="center">
        <template #default="scope">
          <el-tag
            v-if="scope.row.avg_response_time"
            size="small"
            :type="scope.row.avg_response_time < 1000 ? 'success' : scope.row.avg_response_time < 3000 ? 'warning' : 'danger'"
            effect="light"
          >
            {{ Math.round(scope.row.avg_response_time) }}ms
          </el-tag>
          <span v-else style="color: #c0c4cc;">-</span>
        </template>
      </el-table-column>

      <!-- 描述 -->
      <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip>
        <template #default="scope">
          <span style="color: #606266; font-size: 12px;">
            {{ scope.row.description || '暂无描述' }}
          </span>
        </template>
      </el-table-column>

      <!-- 操作 -->
      <el-table-column label="操作" width="160" align="center" fixed="right">
        <template #default="scope">
          <div style="display: flex; gap: 8px; justify-content: center;">
            <el-button type="primary" size="small" @click="editRegistry(scope.row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="deleteRegistry(scope.row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      :title="isEditing ? '编辑镜像源配置' : '添加镜像源配置'"
      v-model="showAddDialog"
      width="650px"
      @close="handleDialogClose"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      draggable
    >
      <template #header>
        <div style="display: flex; align-items: center; gap: 8px;">
          <el-icon size="18" color="#409eff">
            <Plus v-if="!isEditing" />
            <Edit v-else />
          </el-icon>
          <span style="font-weight: 600; color: #303133;">
            {{ isEditing ? '编辑镜像源配置' : '添加镜像源配置' }}
          </span>
        </div>
      </template>

      <el-form :model="formData" :rules="rules" ref="formRef" label-width="120px" label-position="right">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="名称" prop="name">
              <el-input
                v-model="formData.name"
                placeholder="请输入镜像源名称，如：Docker Hub Official"
                clearable
                maxlength="50"
                show-word-limit
                size="large"
              >
                <template #prefix>
                  <el-icon><Setting /></el-icon>
                </template>
              </el-input>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="URL" prop="url">
              <el-input
                v-model="formData.url"
                placeholder="请输入镜像源URL，如：https://registry-1.docker.io"
                clearable
                size="large"
              >
                <template #prefix>
                  <el-icon><Link /></el-icon>
                </template>
              </el-input>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="启用状态" prop="enabled">
              <el-switch
                v-model="formData.enabled"
                active-text="启用"
                inactive-text="禁用"
                inline-prompt
                size="large"
              />
              <span style="margin-left: 12px; color: #606266; font-size: 14px;">
                {{ formData.enabled ? '镜像源已启用，将参与负载均衡' : '镜像源已禁用，不会被使用' }}
              </span>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="权重" prop="weight">
              <el-input-number
                v-model="formData.weight"
                :min="1"
                :max="100"
                controls-position="right"
                style="width: 100%;"
                size="large"
              />
              <div style="font-size: 12px; color: #909399; margin-top: 4px;">
                权重优先级 (1-100)
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="12" :offset="0">
            <el-form-item label="超时时间" prop="timeout_seconds" style="margin-left: -20px;">
              <div style="display: flex; align-items: center; gap: 8px;">
                <el-input-number
                  v-model="formData.timeout_seconds"
                  :min="5"
                  :max="300"
                  controls-position="right"
                  style="flex: 1;"
                  size="large"
                />
                <span style="color: #606266; font-weight: 500;">秒</span>
              </div>
              <div style="font-size: 12px; color: #909399; margin-top: 4px;">
                请求超时时间
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="描述" prop="description">
              <el-input
                v-model="formData.description"
                type="textarea"
                :rows="4"
                placeholder="请输入镜像源描述，如：官方Docker Hub镜像源，稳定性高，适合生产环境使用"
                maxlength="200"
                show-word-limit
                resize="none"
                style="font-family: inherit;"
              />
              <div style="font-size: 12px; color: #909399; margin-top: 4px;">
                <el-icon><InfoFilled /></el-icon>
                描述信息将帮助您更好地识别和管理镜像源
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAddDialog = false" size="default">
            <el-icon><Close /></el-icon>
            取消
          </el-button>
          <el-button type="primary" @click="saveRegistry" :loading="saving" size="default">
            <el-icon v-if="!saving">
              <Check v-if="!isEditing" />
              <Edit v-else />
            </el-icon>
            {{ isEditing ? '更新配置' : '创建配置' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { CircleCheck, CircleClose, Refresh, Plus, Edit, Delete, Close, Check } from '@element-plus/icons-vue'
import { apiService as api } from '../services/api'
import { useWebSocket } from '../composables/useWebSocket'

// 响应式数据
const registries = ref([])
const loading = ref(false)
const showAddDialog = ref(false)
const saving = ref(false)
const isEditing = ref(false)
const formRef = ref()

// WebSocket连接
const { connect, disconnect } = useWebSocket()

// 表单数据
const formData = reactive({
  id: null,
  name: '',
  url: '',
  enabled: true,
  weight: 1,
  timeout_seconds: 30,
  description: ''
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入镜像源名称', trigger: 'blur' },
    { min: 2, max: 50, message: '名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  url: [
    { required: true, message: '请输入镜像源URL', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL', trigger: 'blur' }
  ],
  weight: [
    { required: true, message: '请输入权重', trigger: 'blur' },
    { type: 'number', min: 1, max: 100, message: '权重范围为 1-100', trigger: 'blur' }
  ],
  timeout_seconds: [
    { required: true, message: '请输入超时时间', trigger: 'blur' },
    { type: 'number', min: 5, max: 300, message: '超时时间范围为 5-300 秒', trigger: 'blur' }
  ]
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 只加载配置的镜像源
    const configsData = await api.getRegistryConfigs()
    console.log('Configs response:', configsData)

    // 获取状态数据
    let statusData = []
    try {
      const registriesResponse = await api.getRegistries()
      statusData = registriesResponse.registries || []
      console.log('Status response:', registriesResponse)
    } catch (error) {
      console.warn('Failed to load registry status:', error)
    }

    // 合并配置和状态数据
    const statusMap = new Map()
    statusData.forEach(status => {
      statusMap.set(status.name, status)
    })

    registries.value = configsData.map(config => {
      const status = statusMap.get(config.name) || {}
      return {
        ...config,
        ...status,
        switching: false
      }
    })

    console.log('Final registries:', registries.value)
  } catch (error) {
    console.error('Failed to load data:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  loadData()
}

// 切换镜像源状态
const toggleRegistry = async (registry) => {
  registry.switching = true
  try {
    if (registry.enabled) {
      await api.enableRegistry(registry.name)
      ElMessage.success(`镜像源 ${registry.name} 已启用`)
    } else {
      await api.disableRegistry(registry.name)
      ElMessage.success(`镜像源 ${registry.name} 已禁用`)
    }
    await loadData()
  } catch (error) {
    console.error('Failed to toggle registry:', error)
    ElMessage.error(`操作失败: ${error.message}`)
    // 恢复状态
    registry.enabled = !registry.enabled
  } finally {
    registry.switching = false
  }
}

// 重置表单
const resetForm = () => {
  formData.id = null
  formData.name = ''
  formData.url = ''
  formData.enabled = true
  formData.weight = 1
  formData.timeout_seconds = 30
  formData.description = ''
  isEditing.value = false
}

// 编辑镜像源
const editRegistry = (registry) => {
  formData.id = registry.id
  formData.name = registry.name
  formData.url = registry.url
  formData.enabled = registry.enabled !== undefined ? registry.enabled : true
  formData.weight = registry.weight || 1
  formData.timeout_seconds = registry.timeout_seconds || 30
  formData.description = registry.description || ''
  isEditing.value = true
  showAddDialog.value = true
}

// 保存镜像源
const saveRegistry = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
  } catch (error) {
    return
  }

  saving.value = true
  try {
    if (isEditing.value) {
      await api.updateRegistryConfig(formData.id, formData)
      ElMessage.success('镜像源配置更新成功')
    } else {
      await api.createRegistryConfig(formData)
      ElMessage.success('镜像源配置创建成功')
    }
    
    showAddDialog.value = false
    resetForm()
    await loadData()
  } catch (error) {
    console.error('Failed to save registry:', error)
    ElMessage.error(isEditing.value ? '更新镜像源配置失败' : '创建镜像源配置失败')
  } finally {
    saving.value = false
  }
}

// 删除镜像源
const deleteRegistry = async (registry) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除镜像源 "${registry.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await api.deleteRegistryConfig(registry.id)
    ElMessage.success('镜像源配置删除成功')
    await loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete registry:', error)
      ElMessage.error('删除镜像源配置失败')
    }
  }
}

// 处理对话框关闭
const handleDialogClose = () => {
  resetForm()
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 组件挂载时加载数据并连接WebSocket
onMounted(() => {
  loadData()
  connect((data) => {
    if (data.type === 'registry_update') {
      loadData()
    }
  })
})

// 组件卸载时断开WebSocket
onUnmounted(() => {
  disconnect()
})

// 监听对话框状态变化
watch(() => showAddDialog.value, (newVal) => {
  if (!newVal) {
    handleDialogClose()
  }
})
</script>

<style scoped>
.registry-manager {
  padding: 24px;
  background: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.registry-table {
  width: 100%;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table th) {
  background: #fafafa;
  color: #606266;
  font-weight: 600;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table tr:hover > td) {
  background-color: #f5f7fa;
}

/* 状态标签样式 */
:deep(.el-tag) {
  border-radius: 4px;
  font-weight: 500;
}

/* 开关样式 */
:deep(.el-switch) {
  --el-switch-on-color: #67c23a;
  --el-switch-off-color: #dcdfe6;
}

.stats-info {
  font-size: 12px;
  line-height: 1.4;
  padding: 4px 0;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 3px;
  padding: 1px 0;
}

.stat-label {
  color: #909399;
  font-size: 11px;
}

.stat-value {
  font-weight: 600;
  font-size: 12px;
}

.stat-value.success {
  color: #67c23a;
}

.stat-value.error {
  color: #f56c6c;
}

/* 按钮样式优化 */
:deep(.el-button--small) {
  padding: 5px 12px;
  font-size: 12px;
  border-radius: 4px;
}

:deep(.el-button--primary) {
  background: #409eff;
  border-color: #409eff;
}

:deep(.el-button--danger) {
  background: #f56c6c;
  border-color: #f56c6c;
}

/* 对话框样式 */
:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .registry-manager {
    padding: 16px;
  }

  .header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .stats-info {
    font-size: 11px;
  }

  :deep(.el-table .cell) {
    padding: 8px;
  }
}

/* 加载状态样式 */
:deep(.el-loading-mask) {
  border-radius: 8px;
}

/* 空状态样式 */
:deep(.el-table__empty-block) {
  padding: 40px 0;
}

:deep(.el-table__empty-text) {
  color: #909399;
}
</style>
