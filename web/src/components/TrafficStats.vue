<template>
  <div class="traffic-stats">
    <!-- 流量概览卡片 -->
    <el-row :gutter="20" class="overview-cards">
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon traffic">
              <el-icon size="24"><DataLine /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">总流量</div>
              <div class="card-value">{{ formatBytes(totalTraffic) }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon download">
              <el-icon size="24"><Download /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">下载流量</div>
              <div class="card-value">{{ formatBytes(totalBytesOut) }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon upload">
              <el-icon size="24"><Upload /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">上传流量</div>
              <div class="card-value">{{ formatBytes(totalBytesIn) }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon users">
              <el-icon size="24"><User /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">独立IP</div>
              <div class="card-value">{{ uniqueIPs }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 时间范围选择 -->
    <el-row :gutter="20" class="controls">
      <el-col :span="24">
        <el-card>
          <div class="control-bar">
            <div class="time-range">
              <el-radio-group v-model="timeRange" @change="onTimeRangeChange">
                <el-radio-button label="24h">24小时</el-radio-button>
                <el-radio-button label="7d">7天</el-radio-button>
                <el-radio-button label="30d">30天</el-radio-button>
              </el-radio-group>
            </div>
            <el-button type="primary" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>流量趋势</span>
          </template>
          <div class="chart-container">
            <canvas ref="trafficChart" width="400" height="200"></canvas>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>镜像源流量分布</span>
          </template>
          <div class="chart-container">
            <canvas ref="registryChart" width="400" height="200"></canvas>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- Top IP流量消耗 -->
    <el-row :gutter="20" class="tables-section">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>Top IP流量消耗</span>
          </template>
          <el-table :data="topTrafficIPs" style="width: 100%" max-height="400">
            <el-table-column prop="ip" label="IP地址" width="150" />
            <el-table-column label="总流量" width="120">
              <template #default="scope">
                {{ formatBytes(scope.row.bytes_in + scope.row.bytes_out) }}
              </template>
            </el-table-column>
            <el-table-column label="下载" width="100">
              <template #default="scope">
                {{ formatBytes(scope.row.bytes_out) }}
              </template>
            </el-table-column>
            <el-table-column label="上传" width="100">
              <template #default="scope">
                {{ formatBytes(scope.row.bytes_in) }}
              </template>
            </el-table-column>
            <el-table-column prop="request_count" label="请求数" width="80" />
            <el-table-column label="最后访问" min-width="120">
              <template #default="scope">
                {{ formatTime(scope.row.last_seen) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>镜像源流量统计</span>
          </template>
          <el-table :data="registryStats" style="width: 100%" max-height="400">
            <el-table-column prop="registry_name" label="镜像源" width="150" />
            <el-table-column label="总流量" width="120">
              <template #default="scope">
                {{ formatBytes(scope.row.total_bytes_in + scope.row.total_bytes_out) }}
              </template>
            </el-table-column>
            <el-table-column prop="total_requests" label="请求数" width="80" />
            <el-table-column label="成功率" width="80">
              <template #default="scope">
                {{ scope.row.success_rate.toFixed(1) }}%
              </template>
            </el-table-column>
            <el-table-column label="平均响应时间" min-width="120">
              <template #default="scope">
                {{ scope.row.avg_response_time }}ms
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue'
import { DataLine, Download, Upload, User, Refresh } from '@element-plus/icons-vue'
import { Chart, registerables } from 'chart.js'
import { apiService as api } from '../services/api'

Chart.register(...registerables)

export default {
  name: 'TrafficStats',
  components: {
    DataLine,
    Download,
    Upload,
    User,
    Refresh
  },
  setup() {
    const timeRange = ref('24h')
    const hourlyStats = ref([])
    const dailyStats = ref([])
    const topTrafficIPs = ref([])
    const registryStats = ref([])
    
    const trafficChart = ref(null)
    const registryChart = ref(null)
    let trafficChartInstance = null
    let registryChartInstance = null

    // 计算总流量
    const totalBytesIn = computed(() => {
      return registryStats.value.reduce((sum, stat) => sum + stat.total_bytes_in, 0)
    })

    const totalBytesOut = computed(() => {
      return registryStats.value.reduce((sum, stat) => sum + stat.total_bytes_out, 0)
    })

    const totalTraffic = computed(() => {
      return totalBytesIn.value + totalBytesOut.value
    })

    const uniqueIPs = computed(() => {
      return topTrafficIPs.value.length
    })

    // 格式化字节数
    const formatBytes = (bytes) => {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    // 格式化时间
    const formatTime = (timeStr) => {
      if (!timeStr) return '-'
      const date = new Date(timeStr)
      return date.toLocaleString('zh-CN')
    }

    // 加载数据
    const loadData = async () => {
      try {
        const hours = timeRange.value === '24h' ? 24 : timeRange.value === '7d' ? 168 : 720
        
        const [hourlyData, registryData, topIPsData] = await Promise.all([
          api.getHourlyTrafficStats(hours),
          api.getRegistryTrafficStats(hours),
          api.getTopTrafficIPs(20)
        ])
        
        hourlyStats.value = hourlyData.stats || []
        registryStats.value = registryData.stats || []
        topTrafficIPs.value = topIPsData.ips || []
        
        updateCharts()
      } catch (error) {
        console.error('Failed to load traffic data:', error)
      }
    }

    // 更新图表
    const updateCharts = async () => {
      await nextTick()
      updateTrafficChart()
      updateRegistryChart()
    }

    // 更新流量趋势图表
    const updateTrafficChart = () => {
      if (trafficChartInstance) {
        trafficChartInstance.destroy()
      }

      if (trafficChart.value) {
        const ctx = trafficChart.value.getContext('2d')
        const labels = hourlyStats.value.map(stat => stat.hour.split(' ')[1] || stat.hour)
        
        trafficChartInstance = new Chart(ctx, {
          type: 'line',
          data: {
            labels: labels,
            datasets: [{
              label: '下载流量 (MB)',
              data: hourlyStats.value.map(stat => (stat.total_bytes_out / 1024 / 1024).toFixed(2)),
              borderColor: '#409EFF',
              backgroundColor: 'rgba(64, 158, 255, 0.1)',
              tension: 0.4
            }, {
              label: '上传流量 (MB)',
              data: hourlyStats.value.map(stat => (stat.total_bytes_in / 1024 / 1024).toFixed(2)),
              borderColor: '#67C23A',
              backgroundColor: 'rgba(103, 194, 58, 0.1)',
              tension: 0.4
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              y: {
                beginAtZero: true
              }
            }
          }
        })
      }
    }

    // 更新镜像源图表
    const updateRegistryChart = () => {
      if (registryChartInstance) {
        registryChartInstance.destroy()
      }

      if (registryChart.value && registryStats.value.length > 0) {
        const ctx = registryChart.value.getContext('2d')
        
        registryChartInstance = new Chart(ctx, {
          type: 'doughnut',
          data: {
            labels: registryStats.value.map(stat => stat.registry_name),
            datasets: [{
              data: registryStats.value.map(stat => stat.total_bytes_out + stat.total_bytes_in),
              backgroundColor: [
                '#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399',
                '#53A8FF', '#85CE61', '#EEBC5B', '#F78989', '#B3B6BC'
              ]
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'bottom'
              }
            }
          }
        })
      }
    }

    // 时间范围改变
    const onTimeRangeChange = () => {
      loadData()
    }

    // 刷新数据
    const refreshData = () => {
      loadData()
    }

    onMounted(() => {
      loadData()
    })

    onUnmounted(() => {
      if (trafficChartInstance) {
        trafficChartInstance.destroy()
      }
      if (registryChartInstance) {
        registryChartInstance.destroy()
      }
    })

    return {
      timeRange,
      hourlyStats,
      topTrafficIPs,
      registryStats,
      totalBytesIn,
      totalBytesOut,
      totalTraffic,
      uniqueIPs,
      trafficChart,
      registryChart,
      formatBytes,
      formatTime,
      onTimeRangeChange,
      refreshData
    }
  }
}
</script>

<style scoped>
.traffic-stats {
  padding: 0;
}

.overview-cards {
  margin-bottom: 20px;
}

.overview-card {
  height: 100px;
}

.card-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.card-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: white;
}

.card-icon.traffic {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon.download {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-icon.upload {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-icon.users {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 5px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.controls {
  margin-bottom: 20px;
}

.control-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
  position: relative;
}

.tables-section {
  margin-bottom: 20px;
}
</style>
