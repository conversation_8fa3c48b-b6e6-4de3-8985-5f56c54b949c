<template>
  <div id="app">
    <!-- 登录页面独立显示 -->
    <router-view v-if="$route.path === '/login'" />

    <!-- 主应用布局 -->
    <el-container v-else>
      <!-- 侧边栏 -->
      <el-aside width="200px" class="sidebar">
        <div class="logo">
          <h2>Docker LB</h2>
        </div>
        <el-menu
          :default-active="$route.path"
          router
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
        >
          <el-menu-item index="/dashboard">
            <el-icon><Monitor /></el-icon>
            <span>仪表板</span>
          </el-menu-item>
          <el-menu-item index="/registries">
            <el-icon><Setting /></el-icon>
            <span>镜像源管理</span>
          </el-menu-item>
          <el-menu-item index="/statistics">
            <el-icon><DataAnalysis /></el-icon>
            <span>统计分析</span>
          </el-menu-item>
          <el-menu-item index="/traffic">
            <el-icon><DataLine /></el-icon>
            <span>流量统计</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-container>
        <!-- 头部 -->
        <el-header class="header">
          <div class="header-content">
            <h1>Docker 负载均衡监控系统</h1>
            <div class="header-right">
              <div class="status-indicator">
                <el-badge :value="healthyCount" :max="99" class="item">
                  <el-button size="small">健康节点</el-button>
                </el-badge>
                <el-badge :value="totalCount" :max="99" class="item">
                  <el-button size="small">总节点</el-button>
                </el-badge>
              </div>
              <div class="user-info" v-if="userInfo">
                <el-dropdown @command="handleUserCommand">
                  <span class="user-name">
                    <el-icon><User /></el-icon>
                    {{ userInfo.username }}
                    <el-icon><ArrowDown /></el-icon>
                  </span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="logout">退出登录</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>
        </el-header>

        <!-- 主体内容 -->
        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { Monitor, Setting, DataAnalysis, DataLine, User, ArrowDown } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useWebSocket } from './composables/useWebSocket'
import { authService } from './services/auth'

export default {
  name: 'App',
  components: {
    Monitor,
    Setting,
    DataAnalysis,
    DataLine,
    User,
    ArrowDown
  },
  setup() {
    const router = useRouter()
    const healthyCount = ref(0)
    const totalCount = ref(0)
    const userInfo = ref(null)

    const { connect, disconnect } = useWebSocket()

    // 获取用户信息
    const loadUserInfo = () => {
      userInfo.value = authService.getUserInfo()
    }

    // 处理用户菜单命令
    const handleUserCommand = async (command) => {
      if (command === 'logout') {
        try {
          await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })

          authService.logout()
          ElMessage.success('已退出登录')
          router.push('/login')
        } catch {
          // 用户取消
        }
      }
    }

    onMounted(() => {
      loadUserInfo()

      // 连接WebSocket
      connect((data) => {
        if (data.type === 'stats_update' && data.registries) {
          totalCount.value = data.registries.length
          healthyCount.value = data.registries.filter(r => r.healthy).length
        }
      })
    })

    onUnmounted(() => {
      disconnect()
    })

    return {
      healthyCount,
      totalCount,
      userInfo,
      handleUserCommand
    }
  }
}
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#app {
  height: 100vh;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

.sidebar {
  background-color: #304156;
  height: 100vh;
}

.logo {
  padding: 20px;
  text-align: center;
  color: #fff;
  border-bottom: 1px solid #434a50;
}

.logo h2 {
  margin: 0;
  font-size: 18px;
}

.header {
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  padding: 0 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.header-content h1 {
  font-size: 20px;
  color: #303133;
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.status-indicator {
  display: flex;
  gap: 10px;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-name {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  color: #606266;
  font-size: 14px;
}

.user-name:hover {
  color: #409EFF;
}

.main-content {
  background-color: #f5f5f5;
  padding: 20px;
}

.el-menu {
  border-right: none;
}
</style>
