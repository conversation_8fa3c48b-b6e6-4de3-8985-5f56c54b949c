import { ref, onUnmounted } from 'vue'

let ws = null
let reconnectTimer = null
let messageHandlers = []

export function useWebSocket() {
  const connected = ref(false)
  const reconnecting = ref(false)

  const connect = (onMessage) => {
    if (onMessage) {
      messageHandlers.push(onMessage)
    }

    if (ws && ws.readyState === WebSocket.OPEN) {
      return
    }

    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const wsUrl = `${protocol}//${window.location.host}/ws`

    ws = new WebSocket(wsUrl)

    ws.onopen = () => {
      console.log('WebSocket connected')
      connected.value = true
      reconnecting.value = false
      
      if (reconnectTimer) {
        clearTimeout(reconnectTimer)
        reconnectTimer = null
      }
    }

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        messageHandlers.forEach(handler => {
          try {
            handler(data)
          } catch (error) {
            console.error('Error in message handler:', error)
          }
        })
      } catch (error) {
        console.error('Error parsing WebSocket message:', error)
      }
    }

    ws.onclose = () => {
      console.log('WebSocket disconnected')
      connected.value = false
      
      // 自动重连
      if (!reconnecting.value) {
        reconnecting.value = true
        reconnectTimer = setTimeout(() => {
          console.log('Attempting to reconnect...')
          connect()
        }, 3000)
      }
    }

    ws.onerror = (error) => {
      console.error('WebSocket error:', error)
    }
  }

  const disconnect = () => {
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }
    
    if (ws) {
      ws.close()
      ws = null
    }
    
    connected.value = false
    reconnecting.value = false
    messageHandlers = []
  }

  const send = (data) => {
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(data))
    }
  }

  onUnmounted(() => {
    // 移除当前组件的消息处理器
    // 注意：这里简化处理，实际应该更精确地管理处理器
  })

  return {
    connected,
    reconnecting,
    connect,
    disconnect,
    send
  }
}
