# 最小化配置，用于调试卡死问题
server:
  host: "0.0.0.0"
  port: 60010
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "60s"

database:
  enabled: false  # 禁用数据库
  path: "data/docker-lb.db"
  max_connections: 1

cache:
  enabled: false  # 禁用缓存
  path: "cache"
  max_size: "1GB"
  expire_time: "24h"
  enable_manifest_cache: false
  enable_blob_cache: false

load_balancer:
  algorithm: "round_robin"
  max_retries: 1
  retry_delay: "1s"
  concurrent_downloads: false  # 禁用并发下载

health_check:
  enabled: false  # 禁用健康检查
  interval: "1m"
  timeout: "10s"
  failure_threshold: 3
  success_threshold: 2

monitoring:
  enabled: false  # 禁用监控
  interval: "30s"
  retention_days: 7

logging:
  level: "info"
  enable_request_log: true
  enable_error_log: true

auth:
  enabled: false  # 禁用认证
  secret_key: "your-secret-key"
  token_expire: "24h"

concurrent:
  max_workers: 2  # 减少工作协程
  queue_size: 10
