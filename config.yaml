# Docker加速负载均衡配置文件
server:
  port: 60010
  host: "0.0.0.0"

# Docker镜像源配置
registries:
  - name: "Docker Hub Official"
    url: "https://registry-1.docker.io"
    weight: 10
    enabled: true
    timeout: 30s
    
  - name: "阿里云镜像源"
    url: "https://registry.cn-hangzhou.aliyuncs.com"
    weight: 8
    enabled: true
    timeout: 30s
    
  - name: "腾讯云镜像源"
    url: "https://mirror.ccs.tencentyun.com"
    weight: 8
    enabled: true
    timeout: 30s
    
  - name: "毫秒镜像"
    url: "https://docker.1ms.run"
    weight: 7
    enabled: true
    timeout: 30s
    
  - name: "开源2"
    url: "https://docker.tbedu.top"
    weight: 6
    enabled: true
    timeout: 30s

  - name: "轩辕镜像"
    url: "https://docker.xuanyuan.me"
    weight: 5
    enabled: true
    timeout: 30s

  - name: "自建镜像"
    url: "https://docker.1024008.icu"
    weight: 5
    enabled: true
    timeout: 30s
# 负载均衡策略
load_balancer:
  strategy: "weighted_round_robin"  # 支持: round_robin, weighted_round_robin, least_connections, hash
  health_check_interval: 30s
  max_retries: 3
  retry_delay: 5s
  enable_concurrent_download: true  # 启用多镜像并发下载

# 健康检查配置
health_check:
  enabled: true
  endpoint: "/v2/"
  interval: 1m  # 每分钟检查一次
  timeout: 10s
  unhealthy_threshold: 3
  healthy_threshold: 2
  disable_threshold: 5      # 连续失败5次后禁用镜像源
  revive_interval: 1h       # 禁用后每1小时检查一次复活

# 日志配置
logging:
  level: "info"  # debug, info, warn, error
  file: "logs/docker-lb.log"
  max_size: 100  # MB
  max_backups: 5
  max_age: 30    # days
  enable_request_log: true  # 启用详细的请求日志

# 监控配置
monitoring:
  enabled: true
  metrics_port: 60020  # 监控指标端口（预留）
  websocket_port: 60030  # WebSocket端口（预留，当前WebSocket运行在主服务器端口上）

# 数据库配置
database:
  type: "sqlite"
  path: "data/docker-lb.db"
  max_connections: 10

# 认证配置
auth:
  enabled: true
  admin_username: "admin"
  admin_password: "qaz111111."  # 请修改为强密码
  jwt_secret: "your-jwt-secret-key-change-this"  # 请修改JWT密钥
  token_expire: "24h"  # Token过期时间

# 缓存配置
cache:
  enabled: true
  storage_path: "cache"  # 缓存存储路径
  max_size: "10GB"  # 最大缓存大小
  expire_time: "168h"  # 缓存过期时间 (7天)
  cleanup_interval: "1h"  # 清理检查间隔
  enable_manifest_cache: true  # 启用manifest缓存
  enable_blob_cache: true  # 启用blob缓存
  max_file_size: "1GB"  # 单个文件最大缓存大小
