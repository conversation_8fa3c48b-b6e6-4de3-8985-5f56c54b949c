package main

import (
	"context"
	"docker-lb/internal/cache"
	"docker-lb/internal/config"
	"docker-lb/internal/database"
	"docker-lb/internal/loadbalancer"
	"docker-lb/internal/server"
	"docker-lb/pkg/logger"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"
)

func main() {
	// 加载配置
	cfg, err := config.Load("config.yaml")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志
	logger := logger.New(cfg.Logging)

	// 初始化数据库
	db, err := database.New(&cfg.Database, logger)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	// 初始化缓存
	cacheManager, err := cache.New(&cfg.Cache, logger)
	if err != nil {
		log.Fatalf("Failed to initialize cache: %v", err)
	}
	defer cacheManager.Close()

	// 创建负载均衡器
	lb := loadbalancer.New(cfg, logger, db)

	// 启动健康检查
	if cfg.HealthCheck.Enabled {
		go lb.StartHealthCheck()
	}

	// 创建HTTP服务器
	srv := server.New(cfg, lb, logger, db, cacheManager)

	// 启动服务器
	go func() {
		logger.Info("Starting Docker Load Balancer on %s:%d", cfg.Server.Host, cfg.Server.Port)
		if err := srv.Start(); err != nil && err != http.ErrServerClosed {
			logger.Error("Server failed to start: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down server...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 停止健康检查
	if cfg.HealthCheck.Enabled {
		lb.StopHealthCheck()
	}

	if err := srv.Shutdown(ctx); err != nil {
		logger.Error("Server forced to shutdown: %v", err)
	}

	logger.Info("Server exited")
}
