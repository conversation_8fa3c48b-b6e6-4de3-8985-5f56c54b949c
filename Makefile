# Docker负载均衡器 Makefile

.PHONY: help build run stop restart clean test docker-build docker-run frontend backend deps

# 默认目标
.DEFAULT_GOAL := help

# 变量定义
APP_NAME := docker-lb
VERSION := $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
BUILD_TIME := $(shell date +%Y-%m-%d_%H:%M:%S)
GO_VERSION := $(shell go version | awk '{print $$3}')

# 构建标志
LDFLAGS := -X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GoVersion=$(GO_VERSION)

# 帮助信息
help: ## 显示帮助信息
	@echo "Docker负载均衡器 - 可用命令:"
	@echo ""
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""

# 安装依赖
deps: ## 安装项目依赖
	@echo "安装Go依赖..."
	go mod tidy
	go mod download
	@echo "安装前端依赖..."
	cd web && npm install

# 构建后端
backend: ## 构建Go后端
	@echo "构建后端应用..."
	mkdir -p bin
	CGO_ENABLED=0 go build -ldflags "$(LDFLAGS)" -o bin/$(APP_NAME) cmd/main.go
	@echo "后端构建完成: bin/$(APP_NAME)"

# 构建前端
frontend: ## 构建Vue前端
	@echo "构建前端应用..."
	cd web && npm run build
	@echo "前端构建完成: web/dist/"

# 构建所有
build: backend frontend ## 构建后端和前端
	@echo "构建完成!"

# 运行开发环境
dev: ## 启动开发环境
	@echo "启动开发环境..."
	@echo "后端: http://localhost:60010"
	@echo "前端: http://localhost:60020"
	@echo ""
	@echo "启动后端服务..."
	go run cmd/main.go &
	@echo "启动前端服务..."
	cd web && npm run dev

# 运行生产环境
run: build ## 构建并运行生产环境
	@echo "启动生产环境..."
	mkdir -p logs
	./bin/$(APP_NAME)

# 启动服务
start: ## 使用脚本启动服务
	@chmod +x scripts/start.sh
	@./scripts/start.sh

# 停止服务
stop: ## 停止服务
	@chmod +x scripts/stop.sh
	@./scripts/stop.sh

# 重启服务
restart: ## 重启服务
	@chmod +x scripts/restart.sh
	@./scripts/restart.sh

# 查看日志
logs: ## 查看应用日志
	@if [ -f logs/docker-lb.log ]; then \
		tail -f logs/docker-lb.log; \
	else \
		echo "日志文件不存在: logs/docker-lb.log"; \
	fi

# 查看状态
status: ## 查看服务状态
	@echo "检查服务状态..."
	@if pgrep -f "$(APP_NAME)" > /dev/null; then \
		echo "✅ 服务正在运行"; \
		echo "进程ID: $$(pgrep -f "$(APP_NAME)")"; \
		echo "监听端口: $$(lsof -i :60010 -t 2>/dev/null || echo "未知")"; \
	else \
		echo "❌ 服务未运行"; \
	fi
	@echo ""
	@echo "健康检查:"
	@curl -s http://localhost:60010/api/v1/health 2>/dev/null | jq . || echo "服务不可访问"

# 运行测试
test: ## 运行测试
	@echo "运行Go测试..."
	go test -v ./...
	@echo "运行前端测试..."
	cd web && npm test

# 代码格式化
fmt: ## 格式化代码
	@echo "格式化Go代码..."
	go fmt ./...
	@echo "格式化前端代码..."
	cd web && npm run lint

# 代码检查
lint: ## 代码静态检查
	@echo "Go代码检查..."
	golangci-lint run ./... || echo "请安装 golangci-lint"
	@echo "前端代码检查..."
	cd web && npm run lint

# 清理构建文件
clean: ## 清理构建文件
	@echo "清理构建文件..."
	rm -rf bin/
	rm -rf web/dist/
	rm -rf logs/*.log
	go clean -cache
	@echo "清理完成"

# Docker构建
docker-build: ## 构建Docker镜像
	@echo "构建Docker镜像..."
	docker build -t $(APP_NAME):$(VERSION) .
	docker tag $(APP_NAME):$(VERSION) $(APP_NAME):latest
	@echo "Docker镜像构建完成: $(APP_NAME):$(VERSION)"

# Docker运行
docker-run: ## 运行Docker容器
	@echo "启动Docker容器..."
	docker-compose up -d
	@echo "容器启动完成"
	@echo "Web界面: http://localhost:60020"

# Docker停止
docker-stop: ## 停止Docker容器
	@echo "停止Docker容器..."
	docker-compose down
	@echo "容器已停止"

# Docker日志
docker-logs: ## 查看Docker日志
	docker-compose logs -f

# 安装到系统
install: build ## 安装到系统
	@echo "安装到系统..."
	sudo cp bin/$(APP_NAME) /usr/local/bin/
	sudo mkdir -p /etc/$(APP_NAME)
	sudo cp config.yaml /etc/$(APP_NAME)/
	@echo "安装完成: /usr/local/bin/$(APP_NAME)"

# 卸载
uninstall: ## 从系统卸载
	@echo "从系统卸载..."
	sudo rm -f /usr/local/bin/$(APP_NAME)
	sudo rm -rf /etc/$(APP_NAME)
	@echo "卸载完成"

# 生成版本信息
version: ## 显示版本信息
	@echo "应用名称: $(APP_NAME)"
	@echo "版本: $(VERSION)"
	@echo "构建时间: $(BUILD_TIME)"
	@echo "Go版本: $(GO_VERSION)"

# 健康检查
health: ## 健康检查
	@echo "执行健康检查..."
	@curl -s http://localhost:60010/api/v1/health | jq . || echo "健康检查失败"

# 性能测试
benchmark: ## 性能测试
	@echo "执行性能测试..."
	@echo "测试API响应时间..."
	@for i in {1..10}; do \
		curl -w "响应时间: %{time_total}s\n" -s -o /dev/null http://localhost:60010/api/v1/health; \
	done

# 备份配置
backup: ## 备份配置文件
	@echo "备份配置文件..."
	@mkdir -p backup
	@cp config.yaml backup/config-$(shell date +%Y%m%d_%H%M%S).yaml
	@echo "配置已备份到 backup/ 目录"
