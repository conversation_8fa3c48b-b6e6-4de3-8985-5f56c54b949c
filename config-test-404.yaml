# 测试404错误处理的配置文件
server:
  host: "0.0.0.0"
  port: 60010
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "60s"

# 镜像源配置 - 只使用一个快速响应的镜像源进行测试
registries:
  - name: "腾讯云镜像源"
    url: "https://mirror.ccs.tencentyun.com"
    weight: 10
    enabled: true
    timeout: 10s

database:
  enabled: false
  path: "data/docker-lb.db"
  max_connections: 1

cache:
  enabled: false
  path: "cache"
  max_size: "1GB"
  expire_time: "24h"
  enable_manifest_cache: false
  enable_blob_cache: false

load_balancer:
  algorithm: "round_robin"
  max_retries: 0  # 不重试，直接返回404
  retry_delay: "1s"
  concurrent_downloads: false

health_check:
  enabled: false
  interval: "1m"
  timeout: "10s"
  failure_threshold: 3
  success_threshold: 2

monitoring:
  enabled: false
  interval: "30s"
  retention_days: 7

logging:
  level: "info"
  enable_request_log: true
  enable_error_log: true

auth:
  enabled: false
  secret_key: "your-secret-key"
  token_expire: "24h"

concurrent:
  max_workers: 2
  queue_size: 10
