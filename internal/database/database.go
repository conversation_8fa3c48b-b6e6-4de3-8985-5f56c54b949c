package database

import (
	"context"
	"database/sql"
	"docker-lb/internal/config"
	"docker-lb/pkg/logger"
	"fmt"
	"os"
	"path/filepath"
	"time"

	_ "github.com/mattn/go-sqlite3"
)

// Database 数据库管理器
type Database struct {
	db       *sql.DB
	config   *config.DatabaseConfig
	logger   *logger.Logger
	ipStatsCh chan *IPStatsUpdate // IP统计更新通道
	stopCh    chan struct{}       // 停止信号
}

// RequestLog 请求日志记录
type RequestLog struct {
	ID           int64     `json:"id"`
	Timestamp    time.Time `json:"timestamp"`
	ClientIP     string    `json:"client_ip"`
	Method       string    `json:"method"`
	Path         string    `json:"path"`
	UserAgent    string    `json:"user_agent"`
	Registry     string    `json:"registry"`
	StatusCode   int       `json:"status_code"`
	ResponseTime int64     `json:"response_time"` // 毫秒
	BytesIn      int64     `json:"bytes_in"`
	BytesOut     int64     `json:"bytes_out"`
	Success      bool      `json:"success"`
}

// IPStatsUpdate IP统计更新
type IPStatsUpdate struct {
	IP        string
	BytesIn   int64
	BytesOut  int64
	Timestamp time.Time
}

// TrafficStats 流量统计
type TrafficStats struct {
	TotalRequests int64 `json:"total_requests"`
	TotalBytesIn  int64 `json:"total_bytes_in"`
	TotalBytesOut int64 `json:"total_bytes_out"`
	SuccessRate   float64 `json:"success_rate"`
}

// IPStats IP统计
type IPStats struct {
	IP           string `json:"ip"`
	RequestCount int64  `json:"request_count"`
	BytesIn      int64  `json:"bytes_in"`
	BytesOut     int64  `json:"bytes_out"`
	LastSeen     time.Time `json:"last_seen"`
}

// HourlyTrafficStats 小时流量统计
type HourlyTrafficStats struct {
	Hour            string  `json:"hour"`
	TotalRequests   int64   `json:"total_requests"`
	SuccessRequests int64   `json:"success_requests"`
	TotalBytesIn    int64   `json:"total_bytes_in"`
	TotalBytesOut   int64   `json:"total_bytes_out"`
	UniqueIPs       int64   `json:"unique_ips"`
	SuccessRate     float64 `json:"success_rate"`
}

// RegistryTrafficStats 镜像源流量统计
type RegistryTrafficStats struct {
	RegistryName     string  `json:"registry_name"`
	TotalRequests    int64   `json:"total_requests"`
	SuccessRequests  int64   `json:"success_requests"`
	TotalBytesIn     int64   `json:"total_bytes_in"`
	TotalBytesOut    int64   `json:"total_bytes_out"`
	AvgResponseTime  int64   `json:"avg_response_time"`
	SuccessRate      float64 `json:"success_rate"`
}

// RegistryState 镜像源状态
type RegistryState struct {
	Name              string    `json:"name"`
	Disabled          bool      `json:"disabled"`
	ConsecutiveFails  int       `json:"consecutive_fails"`
	LastHealthCheck   time.Time `json:"last_health_check"`
	LastReviveCheck   time.Time `json:"last_revive_check"`
	DisabledAt        time.Time `json:"disabled_at"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
}

// RegistryConfig 镜像源配置
type RegistryConfig struct {
	ID             int       `json:"id" db:"id"`
	Name           string    `json:"name" db:"name"`
	URL            string    `json:"url" db:"url"`
	Enabled        bool      `json:"enabled" db:"enabled"`
	TimeoutSeconds int       `json:"timeout_seconds" db:"timeout_seconds"`
	Weight         int       `json:"weight" db:"weight"`
	Description    string    `json:"description" db:"description"`
	CreatedAt      time.Time `json:"created_at" db:"created_at"`
	UpdatedAt      time.Time `json:"updated_at" db:"updated_at"`
}

// DailyTrafficSummary 日流量汇总
type DailyTrafficSummary struct {
	Date            string  `json:"date"`
	TotalRequests   int64   `json:"total_requests"`
	TotalBytesIn    int64   `json:"total_bytes_in"`
	TotalBytesOut   int64   `json:"total_bytes_out"`
	UniqueIPs       int64   `json:"unique_ips"`
	SuccessRate     float64 `json:"success_rate"`
}

// New 创建数据库管理器
func New(cfg *config.DatabaseConfig, logger *logger.Logger) (*Database, error) {
	// 确保数据目录存在
	dir := filepath.Dir(cfg.Path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create database directory: %w", err)
	}

	// 打开数据库连接，优化并发性能
	dsn := fmt.Sprintf("%s?_journal_mode=WAL&_synchronous=NORMAL&_cache_size=2000&_busy_timeout=30000&_foreign_keys=ON", cfg.Path)
	db, err := sql.Open("sqlite3", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// 设置连接池，严格限制并发连接数以避免SQLite锁竞争
	maxConns := 3 // 大幅减少并发连接数
	db.SetMaxOpenConns(maxConns)
	db.SetMaxIdleConns(1) // 只保留1个空闲连接
	db.SetConnMaxLifetime(30 * time.Minute) // 缩短连接生命周期

	database := &Database{
		db:        db,
		config:    cfg,
		logger:    logger,
		ipStatsCh: make(chan *IPStatsUpdate, 1000), // 缓冲1000个更新
		stopCh:    make(chan struct{}),
	}

	// 初始化数据库表
	if err := database.initTables(); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to initialize tables: %w", err)
	}

	// 启动IP统计更新处理器
	go database.ipStatsProcessor()

	logger.Info("Database initialized: %s", cfg.Path)
	return database, nil
}

// initTables 初始化数据库表
func (d *Database) initTables() error {
	// 请求日志表
	requestLogSQL := `
	CREATE TABLE IF NOT EXISTS request_logs (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		timestamp DATETIME NOT NULL,
		client_ip TEXT NOT NULL,
		method TEXT NOT NULL,
		path TEXT NOT NULL,
		user_agent TEXT,
		registry TEXT,
		status_code INTEGER NOT NULL,
		response_time INTEGER NOT NULL,
		bytes_in INTEGER DEFAULT 0,
		bytes_out INTEGER DEFAULT 0,
		success BOOLEAN NOT NULL
	);
	
	CREATE INDEX IF NOT EXISTS idx_request_logs_timestamp ON request_logs(timestamp);
	CREATE INDEX IF NOT EXISTS idx_request_logs_client_ip ON request_logs(client_ip);
	CREATE INDEX IF NOT EXISTS idx_request_logs_registry ON request_logs(registry);
	`

	// 流量统计表（按小时聚合）
	trafficStatsSQL := `
	CREATE TABLE IF NOT EXISTS traffic_stats (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		hour_timestamp DATETIME NOT NULL,
		total_requests INTEGER NOT NULL,
		success_requests INTEGER NOT NULL,
		total_bytes_in INTEGER NOT NULL,
		total_bytes_out INTEGER NOT NULL,
		unique_ips INTEGER NOT NULL,
		UNIQUE(hour_timestamp)
	);
	
	CREATE INDEX IF NOT EXISTS idx_traffic_stats_hour ON traffic_stats(hour_timestamp);
	`

	// IP统计表
	ipStatsSQL := `
	CREATE TABLE IF NOT EXISTS ip_stats (
		ip TEXT PRIMARY KEY,
		request_count INTEGER NOT NULL DEFAULT 0,
		bytes_in INTEGER NOT NULL DEFAULT 0,
		bytes_out INTEGER NOT NULL DEFAULT 0,
		first_seen DATETIME NOT NULL,
		last_seen DATETIME NOT NULL
	);
	
	CREATE INDEX IF NOT EXISTS idx_ip_stats_request_count ON ip_stats(request_count DESC);
	CREATE INDEX IF NOT EXISTS idx_ip_stats_last_seen ON ip_stats(last_seen);
	`

	// 镜像源统计表
	registryStatsSQL := `
	CREATE TABLE IF NOT EXISTS registry_stats (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		registry_name TEXT NOT NULL,
		hour_timestamp DATETIME NOT NULL,
		total_requests INTEGER NOT NULL,
		success_requests INTEGER NOT NULL,
		total_bytes_in INTEGER NOT NULL,
		total_bytes_out INTEGER NOT NULL,
		avg_response_time INTEGER NOT NULL,
		UNIQUE(registry_name, hour_timestamp)
	);

	CREATE INDEX IF NOT EXISTS idx_registry_stats_name_hour ON registry_stats(registry_name, hour_timestamp);
	`

	// 镜像源状态表
	registryStatesSQL := `
	CREATE TABLE IF NOT EXISTS registry_states (
		name TEXT PRIMARY KEY,
		disabled BOOLEAN DEFAULT FALSE,
		consecutive_fails INTEGER DEFAULT 0,
		last_health_check DATETIME,
		last_revive_check DATETIME,
		disabled_at DATETIME,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);

	CREATE INDEX IF NOT EXISTS idx_registry_states_disabled ON registry_states(disabled);
	CREATE INDEX IF NOT EXISTS idx_registry_states_updated ON registry_states(updated_at);
	`

	// 镜像源配置表
	registryConfigsSQL := `
	CREATE TABLE IF NOT EXISTS registry_configs (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		name TEXT UNIQUE NOT NULL,
		url TEXT NOT NULL,
		enabled BOOLEAN DEFAULT TRUE,
		timeout_seconds INTEGER DEFAULT 30,
		weight INTEGER DEFAULT 1,
		description TEXT DEFAULT '',
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);

	CREATE INDEX IF NOT EXISTS idx_registry_configs_enabled ON registry_configs(enabled);
	CREATE INDEX IF NOT EXISTS idx_registry_configs_name ON registry_configs(name);
	CREATE INDEX IF NOT EXISTS idx_registry_configs_weight ON registry_configs(weight);
	`

	queries := []string{requestLogSQL, trafficStatsSQL, ipStatsSQL, registryStatsSQL, registryStatesSQL, registryConfigsSQL}
	
	for _, query := range queries {
		if _, err := d.db.Exec(query); err != nil {
			return fmt.Errorf("failed to execute SQL: %w", err)
		}
	}

	return nil
}

// LogRequest 记录请求日志
func (d *Database) LogRequest(log *RequestLog) error {
	query := `
	INSERT INTO request_logs (
		timestamp, client_ip, method, path, user_agent, registry,
		status_code, response_time, bytes_in, bytes_out, success
	) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	_, err := d.db.Exec(query,
		log.Timestamp, log.ClientIP, log.Method, log.Path, log.UserAgent,
		log.Registry, log.StatusCode, log.ResponseTime, log.BytesIn, log.BytesOut, log.Success,
	)

	if err != nil {
		d.logger.Error("Failed to log request: %v", err)
		return err
	}

	// 发送IP统计更新到通道（非阻塞）
	update := &IPStatsUpdate{
		IP:        log.ClientIP,
		BytesIn:   log.BytesIn,
		BytesOut:  log.BytesOut,
		Timestamp: log.Timestamp,
	}

	select {
	case d.ipStatsCh <- update:
		// 成功发送
	default:
		// 通道满了，丢弃这次更新
		d.logger.Warn("IP stats update channel full, dropping update for IP: %s", log.ClientIP)
	}

	return nil
}

// ipStatsProcessor 处理IP统计更新
func (d *Database) ipStatsProcessor() {
	ticker := time.NewTicker(5 * time.Second) // 每5秒批量处理一次
	defer ticker.Stop()

	updates := make(map[string]*IPStatsUpdate)

	for {
		select {
		case update := <-d.ipStatsCh:
			// 合并相同IP的更新
			if existing, exists := updates[update.IP]; exists {
				existing.BytesIn += update.BytesIn
				existing.BytesOut += update.BytesOut
				if update.Timestamp.After(existing.Timestamp) {
					existing.Timestamp = update.Timestamp
				}
			} else {
				updates[update.IP] = update
			}

		case <-ticker.C:
			// 批量处理更新
			if len(updates) > 0 {
				d.batchUpdateIPStats(updates)
				updates = make(map[string]*IPStatsUpdate)
			}

		case <-d.stopCh:
			// 处理剩余的更新
			if len(updates) > 0 {
				d.batchUpdateIPStats(updates)
			}
			d.logger.Info("IP stats processor stopped")
			return
		}
	}
}

// batchUpdateIPStats 批量更新IP统计
func (d *Database) batchUpdateIPStats(updates map[string]*IPStatsUpdate) {
	if len(updates) == 0 {
		return
	}

	tx, err := d.db.Begin()
	if err != nil {
		d.logger.Error("Failed to begin transaction for IP stats: %v", err)
		return
	}
	defer tx.Rollback()

	query := `
	INSERT INTO ip_stats (ip, request_count, bytes_in, bytes_out, first_seen, last_seen)
	VALUES (?, 1, ?, ?, ?, ?)
	ON CONFLICT(ip) DO UPDATE SET
		request_count = request_count + 1,
		bytes_in = bytes_in + ?,
		bytes_out = bytes_out + ?,
		last_seen = ?
	`

	stmt, err := tx.Prepare(query)
	if err != nil {
		d.logger.Error("Failed to prepare IP stats statement: %v", err)
		return
	}
	defer stmt.Close()

	for _, update := range updates {
		_, err := stmt.Exec(update.IP, update.BytesIn, update.BytesOut,
			update.Timestamp, update.Timestamp, update.BytesIn, update.BytesOut, update.Timestamp)
		if err != nil {
			d.logger.Error("Failed to update IP stats for %s: %v", update.IP, err)
		}
	}

	if err := tx.Commit(); err != nil {
		d.logger.Error("Failed to commit IP stats transaction: %v", err)
	} else {
		d.logger.Debug("Batch updated IP stats for %d IPs", len(updates))
	}
}

// GetTrafficStats 获取流量统计
func (d *Database) GetTrafficStats(hours int) (*TrafficStats, error) {
	// 添加超时控制
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	since := time.Now().Add(-time.Duration(hours) * time.Hour)

	query := `
	SELECT
		COUNT(*) as total_requests,
		COALESCE(SUM(bytes_in), 0) as total_bytes_in,
		COALESCE(SUM(bytes_out), 0) as total_bytes_out,
		COALESCE(AVG(CASE WHEN success THEN 1.0 ELSE 0.0 END) * 100, 0) as success_rate
	FROM request_logs
	WHERE timestamp >= ?
	`

	var stats TrafficStats
	err := d.db.QueryRowContext(ctx, query, since).Scan(
		&stats.TotalRequests,
		&stats.TotalBytesIn,
		&stats.TotalBytesOut,
		&stats.SuccessRate,
	)

	if err != nil {
		if err == context.DeadlineExceeded {
			d.logger.Warn("GetTrafficStats query timeout after 10 seconds")
			return &TrafficStats{}, nil // 返回空统计而不是错误
		}
		return nil, fmt.Errorf("failed to get traffic stats: %w", err)
	}

	return &stats, nil
}

// GetTopIPs 获取请求最多的IP
func (d *Database) GetTopIPs(limit int) ([]IPStats, error) {
	query := `
	SELECT ip, request_count, bytes_in, bytes_out, last_seen
	FROM ip_stats
	ORDER BY request_count DESC
	LIMIT ?
	`

	rows, err := d.db.Query(query, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get top IPs: %w", err)
	}
	defer rows.Close()

	var results []IPStats
	for rows.Next() {
		var stat IPStats
		err := rows.Scan(&stat.IP, &stat.RequestCount, &stat.BytesIn, &stat.BytesOut, &stat.LastSeen)
		if err != nil {
			return nil, fmt.Errorf("failed to scan IP stats: %w", err)
		}
		results = append(results, stat)
	}

	return results, nil
}

// GetHourlyTrafficStats 获取小时流量统计
func (d *Database) GetHourlyTrafficStats(hours int) ([]HourlyTrafficStats, error) {
	// 添加超时控制
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	since := time.Now().Add(-time.Duration(hours) * time.Hour)

	query := `
	SELECT
		strftime('%Y-%m-%d %H:00', timestamp) as hour,
		COUNT(*) as total_requests,
		SUM(CASE WHEN success THEN 1 ELSE 0 END) as success_requests,
		COALESCE(SUM(bytes_in), 0) as total_bytes_in,
		COALESCE(SUM(bytes_out), 0) as total_bytes_out,
		COUNT(DISTINCT client_ip) as unique_ips,
		COALESCE(AVG(CASE WHEN success THEN 1.0 ELSE 0.0 END) * 100, 0) as success_rate
	FROM request_logs
	WHERE timestamp >= ?
	GROUP BY strftime('%Y-%m-%d %H:00', timestamp)
	ORDER BY hour DESC
	LIMIT 100
	`

	rows, err := d.db.QueryContext(ctx, query, since)
	if err != nil {
		if err == context.DeadlineExceeded {
			d.logger.Warn("GetHourlyTrafficStats query timeout after 15 seconds")
			return []HourlyTrafficStats{}, nil
		}
		return nil, fmt.Errorf("failed to get hourly traffic stats: %w", err)
	}
	defer rows.Close()

	var results []HourlyTrafficStats
	for rows.Next() {
		var stat HourlyTrafficStats
		err := rows.Scan(
			&stat.Hour, &stat.TotalRequests, &stat.SuccessRequests,
			&stat.TotalBytesIn, &stat.TotalBytesOut, &stat.UniqueIPs, &stat.SuccessRate,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan hourly stats: %w", err)
		}
		results = append(results, stat)
	}

	return results, nil
}

// GetRegistryTrafficStats 获取镜像源流量统计
func (d *Database) GetRegistryTrafficStats(hours int) ([]RegistryTrafficStats, error) {
	// 添加超时控制
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	since := time.Now().Add(-time.Duration(hours) * time.Hour)

	query := `
	SELECT
		registry,
		COUNT(*) as total_requests,
		SUM(CASE WHEN success THEN 1 ELSE 0 END) as success_requests,
		COALESCE(SUM(bytes_in), 0) as total_bytes_in,
		COALESCE(SUM(bytes_out), 0) as total_bytes_out,
		COALESCE(AVG(response_time), 0) as avg_response_time,
		COALESCE(AVG(CASE WHEN success THEN 1.0 ELSE 0.0 END) * 100, 0) as success_rate
	FROM request_logs
	WHERE timestamp >= ? AND registry IS NOT NULL AND registry != ''
	GROUP BY registry
	ORDER BY total_requests DESC
	LIMIT 50
	`

	rows, err := d.db.QueryContext(ctx, query, since.Format("2006-01-02 15:04:05"))
	if err != nil {
		if err == context.DeadlineExceeded {
			d.logger.Warn("GetRegistryTrafficStats query timeout after 15 seconds")
			return []RegistryTrafficStats{}, nil
		}
		return nil, fmt.Errorf("failed to get registry traffic stats: %w", err)
	}
	defer rows.Close()

	var results []RegistryTrafficStats
	for rows.Next() {
		var stat RegistryTrafficStats
		var avgResponseTime sql.NullFloat64
		err := rows.Scan(
			&stat.RegistryName, &stat.TotalRequests, &stat.SuccessRequests,
			&stat.TotalBytesIn, &stat.TotalBytesOut, &avgResponseTime, &stat.SuccessRate,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan registry stats: %w", err)
		}

		if avgResponseTime.Valid {
			stat.AvgResponseTime = int64(avgResponseTime.Float64)
		}

		results = append(results, stat)
	}

	return results, nil
}

// GetDailyTrafficSummary 获取日流量汇总
func (d *Database) GetDailyTrafficSummary(days int) ([]DailyTrafficSummary, error) {
	since := time.Now().AddDate(0, 0, -days)

	query := `
	SELECT
		strftime('%Y-%m-%d', timestamp) as date,
		COUNT(*) as total_requests,
		SUM(bytes_in) as total_bytes_in,
		SUM(bytes_out) as total_bytes_out,
		COUNT(DISTINCT client_ip) as unique_ips,
		AVG(CASE WHEN success THEN 1.0 ELSE 0.0 END) * 100 as success_rate
	FROM request_logs
	WHERE timestamp >= ?
	GROUP BY strftime('%Y-%m-%d', timestamp)
	ORDER BY date DESC
	`

	rows, err := d.db.Query(query, since)
	if err != nil {
		return nil, fmt.Errorf("failed to get daily traffic summary: %w", err)
	}
	defer rows.Close()

	var results []DailyTrafficSummary
	for rows.Next() {
		var stat DailyTrafficSummary
		err := rows.Scan(
			&stat.Date, &stat.TotalRequests, &stat.TotalBytesIn,
			&stat.TotalBytesOut, &stat.UniqueIPs, &stat.SuccessRate,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan daily summary: %w", err)
		}
		results = append(results, stat)
	}

	return results, nil
}

// GetTopTrafficIPs 获取流量消耗最多的IP
func (d *Database) GetTopTrafficIPs(limit int) ([]IPStats, error) {
	// 添加超时控制
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 限制查询数量
	if limit > 100 {
		limit = 100
	}

	query := `
	SELECT ip, request_count, bytes_in, bytes_out, last_seen
	FROM ip_stats
	ORDER BY (bytes_in + bytes_out) DESC
	LIMIT ?
	`

	rows, err := d.db.QueryContext(ctx, query, limit)
	if err != nil {
		if err == context.DeadlineExceeded {
			d.logger.Warn("GetTopTrafficIPs query timeout after 10 seconds")
			return []IPStats{}, nil
		}
		return nil, fmt.Errorf("failed to get top traffic IPs: %w", err)
	}
	defer rows.Close()

	var results []IPStats
	for rows.Next() {
		var stat IPStats
		err := rows.Scan(&stat.IP, &stat.RequestCount, &stat.BytesIn, &stat.BytesOut, &stat.LastSeen)
		if err != nil {
			return nil, fmt.Errorf("failed to scan IP stats: %w", err)
		}
		results = append(results, stat)
	}

	return results, nil
}

// SaveRegistryState 保存镜像源状态
func (d *Database) SaveRegistryState(state *RegistryState) error {
	query := `
	INSERT OR REPLACE INTO registry_states (
		name, disabled, consecutive_fails, last_health_check,
		last_revive_check, disabled_at, updated_at
	) VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
	`

	var lastHealthCheck, lastReviveCheck, disabledAt interface{}

	if !state.LastHealthCheck.IsZero() {
		lastHealthCheck = state.LastHealthCheck.Format("2006-01-02 15:04:05")
	}
	if !state.LastReviveCheck.IsZero() {
		lastReviveCheck = state.LastReviveCheck.Format("2006-01-02 15:04:05")
	}
	if !state.DisabledAt.IsZero() {
		disabledAt = state.DisabledAt.Format("2006-01-02 15:04:05")
	}

	_, err := d.db.Exec(query, state.Name, state.Disabled, state.ConsecutiveFails,
		lastHealthCheck, lastReviveCheck, disabledAt)

	if err != nil {
		return fmt.Errorf("failed to save registry state: %w", err)
	}

	return nil
}

// GetRegistryState 获取镜像源状态
func (d *Database) GetRegistryState(name string) (*RegistryState, error) {
	query := `
	SELECT name, disabled, consecutive_fails, last_health_check,
		   last_revive_check, disabled_at, created_at, updated_at
	FROM registry_states
	WHERE name = ?
	`

	var state RegistryState
	var lastHealthCheck, lastReviveCheck, disabledAt, createdAt, updatedAt sql.NullString

	err := d.db.QueryRow(query, name).Scan(
		&state.Name, &state.Disabled, &state.ConsecutiveFails,
		&lastHealthCheck, &lastReviveCheck, &disabledAt,
		&createdAt, &updatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil // 没有找到记录
		}
		return nil, fmt.Errorf("failed to get registry state: %w", err)
	}

	// 解析时间字段
	if lastHealthCheck.Valid {
		if t, err := time.Parse("2006-01-02 15:04:05", lastHealthCheck.String); err == nil {
			state.LastHealthCheck = t
		}
	}
	if lastReviveCheck.Valid {
		if t, err := time.Parse("2006-01-02 15:04:05", lastReviveCheck.String); err == nil {
			state.LastReviveCheck = t
		}
	}
	if disabledAt.Valid {
		if t, err := time.Parse("2006-01-02 15:04:05", disabledAt.String); err == nil {
			state.DisabledAt = t
		}
	}
	if createdAt.Valid {
		if t, err := time.Parse("2006-01-02 15:04:05", createdAt.String); err == nil {
			state.CreatedAt = t
		}
	}
	if updatedAt.Valid {
		if t, err := time.Parse("2006-01-02 15:04:05", updatedAt.String); err == nil {
			state.UpdatedAt = t
		}
	}

	return &state, nil
}

// GetAllRegistryStates 获取所有镜像源状态
func (d *Database) GetAllRegistryStates() (map[string]*RegistryState, error) {
	query := `
	SELECT name, disabled, consecutive_fails, last_health_check,
		   last_revive_check, disabled_at, created_at, updated_at
	FROM registry_states
	`

	rows, err := d.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("failed to query registry states: %w", err)
	}
	defer rows.Close()

	states := make(map[string]*RegistryState)

	for rows.Next() {
		var state RegistryState
		var lastHealthCheck, lastReviveCheck, disabledAt, createdAt, updatedAt sql.NullString

		err := rows.Scan(
			&state.Name, &state.Disabled, &state.ConsecutiveFails,
			&lastHealthCheck, &lastReviveCheck, &disabledAt,
			&createdAt, &updatedAt,
		)

		if err != nil {
			return nil, fmt.Errorf("failed to scan registry state: %w", err)
		}

		// 解析时间字段
		if lastHealthCheck.Valid {
			if t, err := time.Parse("2006-01-02 15:04:05", lastHealthCheck.String); err == nil {
				state.LastHealthCheck = t
			}
		}
		if lastReviveCheck.Valid {
			if t, err := time.Parse("2006-01-02 15:04:05", lastReviveCheck.String); err == nil {
				state.LastReviveCheck = t
			}
		}
		if disabledAt.Valid {
			if t, err := time.Parse("2006-01-02 15:04:05", disabledAt.String); err == nil {
				state.DisabledAt = t
			}
		}
		if createdAt.Valid {
			if t, err := time.Parse("2006-01-02 15:04:05", createdAt.String); err == nil {
				state.CreatedAt = t
			}
		}
		if updatedAt.Valid {
			if t, err := time.Parse("2006-01-02 15:04:05", updatedAt.String); err == nil {
				state.UpdatedAt = t
			}
		}

		states[state.Name] = &state
	}

	return states, nil
}

// CreateRegistryConfig 创建镜像源配置
func (d *Database) CreateRegistryConfig(config *RegistryConfig) error {
	query := `
	INSERT INTO registry_configs (name, url, enabled, timeout_seconds, weight, description)
	VALUES (?, ?, ?, ?, ?, ?)
	`

	result, err := d.db.Exec(query, config.Name, config.URL, config.Enabled,
		config.TimeoutSeconds, config.Weight, config.Description)

	if err != nil {
		return fmt.Errorf("failed to create registry config: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("failed to get last insert id: %w", err)
	}

	config.ID = int(id)
	config.CreatedAt = time.Now()
	config.UpdatedAt = time.Now()

	return nil
}

// GetRegistryConfig 获取单个镜像源配置
func (d *Database) GetRegistryConfig(id int) (*RegistryConfig, error) {
	query := `
	SELECT id, name, url, enabled, timeout_seconds, weight, description, created_at, updated_at
	FROM registry_configs
	WHERE id = ?
	`

	var config RegistryConfig
	var createdAt, updatedAt sql.NullString

	err := d.db.QueryRow(query, id).Scan(
		&config.ID, &config.Name, &config.URL, &config.Enabled,
		&config.TimeoutSeconds, &config.Weight, &config.Description,
		&createdAt, &updatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get registry config: %w", err)
	}

	// 解析时间字段
	if createdAt.Valid {
		if t, err := time.Parse("2006-01-02 15:04:05", createdAt.String); err == nil {
			config.CreatedAt = t
		}
	}
	if updatedAt.Valid {
		if t, err := time.Parse("2006-01-02 15:04:05", updatedAt.String); err == nil {
			config.UpdatedAt = t
		}
	}

	return &config, nil
}

// GetRegistryConfigByName 根据名称获取镜像源配置
func (d *Database) GetRegistryConfigByName(name string) (*RegistryConfig, error) {
	query := `
	SELECT id, name, url, enabled, timeout_seconds, weight, description, created_at, updated_at
	FROM registry_configs
	WHERE name = ?
	`

	var config RegistryConfig
	var createdAt, updatedAt sql.NullString

	err := d.db.QueryRow(query, name).Scan(
		&config.ID, &config.Name, &config.URL, &config.Enabled,
		&config.TimeoutSeconds, &config.Weight, &config.Description,
		&createdAt, &updatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get registry config by name: %w", err)
	}

	// 解析时间字段
	if createdAt.Valid {
		if t, err := time.Parse("2006-01-02 15:04:05", createdAt.String); err == nil {
			config.CreatedAt = t
		}
	}
	if updatedAt.Valid {
		if t, err := time.Parse("2006-01-02 15:04:05", updatedAt.String); err == nil {
			config.UpdatedAt = t
		}
	}

	return &config, nil
}

// GetAllRegistryConfigs 获取所有镜像源配置
func (d *Database) GetAllRegistryConfigs() ([]*RegistryConfig, error) {
	query := `
	SELECT id, name, url, enabled, timeout_seconds, weight, description, created_at, updated_at
	FROM registry_configs
	ORDER BY weight DESC, name ASC
	`

	rows, err := d.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("failed to query registry configs: %w", err)
	}
	defer rows.Close()

	var configs []*RegistryConfig

	for rows.Next() {
		var config RegistryConfig
		var createdAt, updatedAt sql.NullString

		err := rows.Scan(
			&config.ID, &config.Name, &config.URL, &config.Enabled,
			&config.TimeoutSeconds, &config.Weight, &config.Description,
			&createdAt, &updatedAt,
		)

		if err != nil {
			return nil, fmt.Errorf("failed to scan registry config: %w", err)
		}

		// 解析时间字段
		if createdAt.Valid {
			if t, err := time.Parse("2006-01-02 15:04:05", createdAt.String); err == nil {
				config.CreatedAt = t
			}
		}
		if updatedAt.Valid {
			if t, err := time.Parse("2006-01-02 15:04:05", updatedAt.String); err == nil {
				config.UpdatedAt = t
			}
		}

		configs = append(configs, &config)
	}

	return configs, nil
}

// UpdateRegistryConfig 更新镜像源配置
func (d *Database) UpdateRegistryConfig(config *RegistryConfig) error {
	query := `
	UPDATE registry_configs
	SET name = ?, url = ?, enabled = ?, timeout_seconds = ?, weight = ?,
		description = ?, updated_at = CURRENT_TIMESTAMP
	WHERE id = ?
	`

	result, err := d.db.Exec(query, config.Name, config.URL, config.Enabled,
		config.TimeoutSeconds, config.Weight, config.Description, config.ID)

	if err != nil {
		return fmt.Errorf("failed to update registry config: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("registry config not found")
	}

	config.UpdatedAt = time.Now()

	return nil
}

// DeleteRegistryConfig 删除镜像源配置
func (d *Database) DeleteRegistryConfig(id int) error {
	query := `DELETE FROM registry_configs WHERE id = ?`

	result, err := d.db.Exec(query, id)
	if err != nil {
		return fmt.Errorf("failed to delete registry config: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("registry config not found")
	}

	return nil
}

// DeleteRegistryConfigByName 根据名称删除镜像源配置
func (d *Database) DeleteRegistryConfigByName(name string) error {
	query := `DELETE FROM registry_configs WHERE name = ?`

	result, err := d.db.Exec(query, name)
	if err != nil {
		return fmt.Errorf("failed to delete registry config by name: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("registry config not found")
	}

	return nil
}

// Close 关闭数据库连接
func (d *Database) Close() error {
	// 停止IP统计处理器
	select {
	case <-d.stopCh:
		// 已经停止
	default:
		close(d.stopCh)
	}

	if d.db != nil {
		return d.db.Close()
	}
	return nil
}
