package auth

import (
	"crypto/sha256"
	"docker-lb/internal/config"
	"docker-lb/pkg/logger"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

// AuthManager 认证管理器
type AuthManager struct {
	config *config.AuthConfig
	logger *logger.Logger
}

// Claims JWT声明
type Claims struct {
	Username string `json:"username"`
	jwt.RegisteredClaims
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	Token     string    `json:"token"`
	ExpiresAt time.Time `json:"expires_at"`
	Username  string    `json:"username"`
}

// New 创建认证管理器
func New(cfg *config.AuthConfig, logger *logger.Logger) *AuthManager {
	return &AuthManager{
		config: cfg,
		logger: logger,
	}
}

// hashPassword 哈希密码
func (am *AuthManager) hashPassword(password string) string {
	hash := sha256.Sum256([]byte(password + am.config.JWTSecret))
	return fmt.Sprintf("%x", hash)
}

// validateCredentials 验证凭据
func (am *AuthManager) validateCredentials(username, password string) bool {
	if username != am.config.AdminUsername {
		return false
	}
	
	hashedPassword := am.hashPassword(password)
	expectedHash := am.hashPassword(am.config.AdminPassword)
	
	return hashedPassword == expectedHash
}

// GenerateToken 生成JWT令牌
func (am *AuthManager) GenerateToken(username string) (*LoginResponse, error) {
	// 解析过期时间
	duration, err := time.ParseDuration(am.config.TokenExpire)
	if err != nil {
		duration = 24 * time.Hour // 默认24小时
	}
	
	expiresAt := time.Now().Add(duration)
	
	claims := &Claims{
		Username: username,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Issuer:    "docker-lb",
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(am.config.JWTSecret))
	if err != nil {
		return nil, fmt.Errorf("failed to sign token: %w", err)
	}

	return &LoginResponse{
		Token:     tokenString,
		ExpiresAt: expiresAt,
		Username:  username,
	}, nil
}

// ValidateToken 验证JWT令牌
func (am *AuthManager) ValidateToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(am.config.JWTSecret), nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}

// LoginHandler 登录处理器
func (am *AuthManager) LoginHandler(c *gin.Context) {
	if !am.config.Enabled {
		c.JSON(http.StatusOK, gin.H{
			"message": "Authentication disabled",
			"token":   "no-auth-required",
		})
		return
	}

	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
		})
		return
	}

	if !am.validateCredentials(req.Username, req.Password) {
		am.logger.Warn("Failed login attempt for username: %s from IP: %s", req.Username, c.ClientIP())
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Invalid credentials",
		})
		return
	}

	response, err := am.GenerateToken(req.Username)
	if err != nil {
		am.logger.Error("Failed to generate token: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to generate token",
		})
		return
	}

	am.logger.Info("Successful login for username: %s from IP: %s", req.Username, c.ClientIP())
	c.JSON(http.StatusOK, response)
}

// AuthMiddleware 认证中间件
func (am *AuthManager) AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 如果认证未启用，直接通过
		if !am.config.Enabled {
			c.Next()
			return
		}

		// 获取Authorization头
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Authorization header required",
			})
			c.Abort()
			return
		}

		// 检查Bearer前缀
		const bearerPrefix = "Bearer "
		if !strings.HasPrefix(authHeader, bearerPrefix) {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid authorization header format",
			})
			c.Abort()
			return
		}

		// 提取token
		tokenString := authHeader[len(bearerPrefix):]
		
		// 验证token
		claims, err := am.ValidateToken(tokenString)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid or expired token",
			})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文
		c.Set("username", claims.Username)
		c.Next()
	}
}

// RefreshTokenHandler 刷新令牌处理器
func (am *AuthManager) RefreshTokenHandler(c *gin.Context) {
	if !am.config.Enabled {
		c.JSON(http.StatusOK, gin.H{
			"message": "Authentication disabled",
		})
		return
	}

	username, exists := c.Get("username")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	response, err := am.GenerateToken(username.(string))
	if err != nil {
		am.logger.Error("Failed to refresh token: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to refresh token",
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetUserInfoHandler 获取用户信息处理器
func (am *AuthManager) GetUserInfoHandler(c *gin.Context) {
	if !am.config.Enabled {
		c.JSON(http.StatusOK, gin.H{
			"username": "anonymous",
			"auth_enabled": false,
		})
		return
	}

	username, exists := c.Get("username")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"username": username,
		"auth_enabled": true,
	})
}

// ChangePasswordHandler 修改密码处理器
func (am *AuthManager) ChangePasswordHandler(c *gin.Context) {
	if !am.config.Enabled {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Authentication disabled",
		})
		return
	}

	var req struct {
		OldPassword string `json:"old_password"`
		NewPassword string `json:"new_password"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
		})
		return
	}

	username, exists := c.Get("username")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	// 验证旧密码
	if !am.validateCredentials(username.(string), req.OldPassword) {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid old password",
		})
		return
	}

	// 注意：这里只是演示，实际应用中应该将新密码保存到配置文件或数据库
	am.logger.Info("Password change requested for user: %s", username)
	c.JSON(http.StatusOK, gin.H{
		"message": "Password change request received. Please update configuration manually.",
	})
}
