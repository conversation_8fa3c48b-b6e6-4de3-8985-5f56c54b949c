package loadbalancer

import (
	"context"
	"docker-lb/internal/config"
	"docker-lb/internal/database"
	"docker-lb/pkg/logger"
	"net/http"
	"net/url"
	"time"
)

// HealthChecker 健康检查器
type HealthChecker struct {
	lb     *LoadBalancer
	config *config.Config
	logger *logger.Logger
	client *http.Client
	db     *database.Database
	stop   chan struct{}
}

// NewHealthChecker 创建健康检查器
func NewHealthChecker(lb *LoadBalancer, cfg *config.Config, logger *logger.Logger, db *database.Database) *HealthChecker {
	return &HealthChecker{
		lb:     lb,
		config: cfg,
		logger: logger,
		client: &http.Client{
			Timeout: cfg.HealthCheck.Timeout,
		},
		db:   db,
		stop: make(chan struct{}),
	}
}

// Start 启动健康检查
func (hc *HealthChecker) Start() {
	hc.logger.Info("Starting health checker with interval: %v", hc.config.HealthCheck.Interval)

	// 恢复镜像源状态
	hc.restoreRegistryStates()

	ticker := time.NewTicker(hc.config.HealthCheck.Interval)
	defer ticker.Stop()

	hc.logger.Info("Health checker will start checking in %v", hc.config.HealthCheck.Interval)

	for {
		select {
		case <-ticker.C:
			hc.checkAll()
		case <-hc.stop:
			hc.logger.Info("Health checker stopped")
			return
		}
	}
}

// Stop 停止健康检查
func (hc *HealthChecker) Stop() {
	select {
	case <-hc.stop:
		// 已经停止
		return
	default:
		close(hc.stop)
	}
}

// checkAll 检查所有镜像源
func (hc *HealthChecker) checkAll() {
	registries := hc.lb.GetRegistries()

	// 使用有限的并发数，避免创建过多goroutine
	maxConcurrent := 3
	if len(registries) < maxConcurrent {
		maxConcurrent = len(registries)
	}

	semaphore := make(chan struct{}, maxConcurrent)

	for _, registry := range registries {
		semaphore <- struct{}{} // 获取信号量
		go func(reg *Registry) {
			defer func() { <-semaphore }() // 释放信号量
			hc.checkRegistry(reg)
		}(registry)
	}
}

// checkRegistry 检查单个镜像源
func (hc *HealthChecker) checkRegistry(registry *Registry) {
	// 快速检查是否需要进行健康检查
	registry.Mu.RLock()
	disabled := registry.Disabled
	lastReviveCheck := registry.LastReviveCheck
	registryName := registry.Config.Name
	registry.Mu.RUnlock()

	// 如果镜像源被禁用，检查是否需要进行复活检查
	if disabled {
		now := time.Now()
		if now.Sub(lastReviveCheck) < hc.config.HealthCheck.ReviveInterval {
			// 还没到复活检查时间，跳过
			return
		}

		// 进行复活检查
		hc.logger.Debug("Performing revive check for disabled registry: %s", registryName)

		// 更新复活检查时间
		registry.Mu.Lock()
		registry.LastReviveCheck = now
		registry.Mu.Unlock()
	}

	start := time.Now()
	healthy := hc.performHealthCheck(registry) // 使用不需要锁的版本
	responseTime := time.Since(start)

	// 获取写锁更新状态
	registry.Mu.Lock()
	registry.Stats.LastHealthCheck = time.Now()

	// 更新健康检查响应时间（在锁外调用）
	registry.Stats.HealthCheckResponse = responseTime

	// 获取当前状态用于日志
	wasDisabled := registry.Disabled
	wasHealthy := registry.Healthy
	currentRegistryName := registry.Config.Name

	if healthy {
		// 健康检查通过
		if wasDisabled {
			// 复活成功
			registry.Disabled = false
			registry.Stats.ConsecutiveFails = 0
			registry.Healthy = true
			registry.Mu.Unlock() // 释放锁后记录日志
			hc.logger.Info("🎉 Registry %s has been revived and is now available", currentRegistryName)
		} else if !wasHealthy {
			// 从不健康恢复为健康
			registry.Healthy = true
			registry.Stats.ConsecutiveFails = 0
			registry.Mu.Unlock() // 释放锁后记录日志
			hc.logger.Info("✅ Registry %s is now healthy", currentRegistryName)
		} else {
			registry.Healthy = true
			registry.Stats.ConsecutiveFails = 0
			registry.Mu.Unlock()
		}
	} else {
		// 健康检查失败
		registry.Stats.ConsecutiveFails++
		consecutiveFails := registry.Stats.ConsecutiveFails

		if wasDisabled {
			// 已禁用的镜像源复活失败
			registry.Mu.Unlock() // 释放锁后记录日志
			hc.logger.Debug("Revive check failed for disabled registry %s (attempt %d)",
				currentRegistryName, consecutiveFails)
		} else {
			// 检查是否需要禁用
			disableThreshold := hc.config.HealthCheck.DisableThreshold
			if disableThreshold <= 0 {
				disableThreshold = 5 // 默认5次失败后禁用
			}

			if consecutiveFails >= disableThreshold {
				// 禁用镜像源
				registry.Disabled = true
				registry.Healthy = false
				registry.LastReviveCheck = time.Now()
				registry.Mu.Unlock() // 释放锁后记录日志
				hc.logger.Warn("🚫 Registry %s has been DISABLED after %d consecutive failures. Will check for revival every %v",
					currentRegistryName, consecutiveFails, hc.config.HealthCheck.ReviveInterval)
			} else if consecutiveFails >= hc.config.HealthCheck.UnhealthyThreshold {
				// 标记为不健康但不禁用
				shouldLog := wasHealthy
				registry.Healthy = false
				registry.Mu.Unlock() // 释放锁后记录日志
				if shouldLog {
					hc.logger.Warn("⚠️ Registry %s marked as unhealthy after %d consecutive failures",
						currentRegistryName, consecutiveFails)
				}
			} else {
				registry.Mu.Unlock()
			}
		}
	}

	// 保存状态到数据库（异步，避免阻塞）
	if hc.db != nil {
		// 获取需要保存的状态数据
		registry.Mu.RLock()
		state := &database.RegistryState{
			Name:             registry.Config.Name,
			Disabled:         registry.Disabled,
			ConsecutiveFails: registry.Stats.ConsecutiveFails,
			LastHealthCheck:  registry.Stats.LastHealthCheck,
			LastReviveCheck:  registry.LastReviveCheck,
		}

		if registry.Disabled && !registry.Stats.LastHealthCheck.IsZero() {
			state.DisabledAt = registry.Stats.LastHealthCheck
		}
		registryName := registry.Config.Name
		registry.Mu.RUnlock()

		go func() {
			if err := hc.db.SaveRegistryState(state); err != nil {
				hc.logger.Error("Failed to save registry state for %s: %v", registryName, err)
			}
		}()
	}

	// 健康检查不更新请求统计，只更新健康状态相关信息
	// 请求统计只在实际代理请求时更新
}

// performHealthCheck 执行健康检查
func (hc *HealthChecker) performHealthCheck(registry *Registry) bool {
	registry.Mu.RLock()
	defer registry.Mu.RUnlock()
	return hc.performHealthCheckLocked(registry)
}

// performHealthCheckLocked 执行健康检查（假设已持有锁）
func (hc *HealthChecker) performHealthCheckLocked(registry *Registry) bool {
	if !hc.config.HealthCheck.Enabled {
		return true
	}

	// 构建健康检查URL
	baseURL, err := url.Parse(registry.Config.URL)
	if err != nil {
		hc.logger.Error("Invalid registry URL %s: %v", registry.Config.URL, err)
		return false
	}

	healthURL := baseURL.ResolveReference(&url.URL{Path: hc.config.HealthCheck.Endpoint})

	// 创建请求
	ctx, cancel := context.WithTimeout(context.Background(), hc.config.HealthCheck.Timeout)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "GET", healthURL.String(), nil)
	if err != nil {
		hc.logger.Error("Failed to create health check request for %s: %v", registry.Config.Name, err)
		return false
	}

	// 设置User-Agent
	req.Header.Set("User-Agent", "Docker-LB-HealthChecker/1.0")

	// 执行请求
	resp, err := hc.client.Do(req)
	if err != nil {
		hc.logger.Debug("Health check failed for %s: %v", registry.Config.Name, err)
		return false
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode >= 200 && resp.StatusCode < 400 {
		hc.logger.Debug("Health check passed for %s: %d", registry.Config.Name, resp.StatusCode)
		return true
	}

	hc.logger.Debug("Health check failed for %s: status %d", registry.Config.Name, resp.StatusCode)
	return false
}

// GetHealthStatus 获取健康状态摘要
func (hc *HealthChecker) GetHealthStatus() map[string]interface{} {
	registries := hc.lb.GetRegistries()
	
	status := map[string]interface{}{
		"total_registries":   len(registries),
		"healthy_registries": 0,
		"registries":         make([]map[string]interface{}, 0, len(registries)),
	}

	for _, registry := range registries {
		registry.Mu.RLock()
		regStatus := map[string]interface{}{
			"name":                   registry.Config.Name,
			"url":                    registry.Config.URL,
			"healthy":                registry.Healthy,
			"weight":                 registry.Config.Weight,
			"total_requests":         registry.Stats.TotalRequests,
			"success_requests":       registry.Stats.SuccessRequests,
			"failed_requests":        registry.Stats.FailedRequests,
			"consecutive_fails":      registry.Stats.ConsecutiveFails,
			"average_response":       registry.Stats.AverageResponse.Milliseconds(),
			"health_check_response":  registry.Stats.HealthCheckResponse.Milliseconds(),
			"last_health_check":      registry.Stats.LastHealthCheck.Format(time.RFC3339),
		}

		if registry.Healthy {
			status["healthy_registries"] = status["healthy_registries"].(int) + 1
		}

		status["registries"] = append(status["registries"].([]map[string]interface{}), regStatus)
		registry.Mu.RUnlock()
	}

	return status
}

// restoreRegistryStates 恢复镜像源状态
func (hc *HealthChecker) restoreRegistryStates() {
	if hc.db == nil {
		return
	}

	states, err := hc.db.GetAllRegistryStates()
	if err != nil {
		hc.logger.Error("Failed to restore registry states: %v", err)
		return
	}

	registries := hc.lb.GetRegistries()
	restoredCount := 0

	for _, registry := range registries {
		if state, exists := states[registry.Config.Name]; exists {
			registry.Mu.Lock()

			// 恢复状态
			registry.Disabled = state.Disabled
			registry.Stats.ConsecutiveFails = state.ConsecutiveFails
			registry.LastReviveCheck = state.LastReviveCheck

			// 如果被禁用，设置为不健康
			if state.Disabled {
				registry.Healthy = false
				hc.logger.Info("🔄 Restored disabled state for registry: %s (disabled since %v)",
					registry.Config.Name, state.DisabledAt.Format("2006-01-02 15:04:05"))
			}

			registry.Mu.Unlock()
			restoredCount++
		}
	}

	if restoredCount > 0 {
		hc.logger.Info("✅ Restored states for %d registries from database", restoredCount)
	}
}
