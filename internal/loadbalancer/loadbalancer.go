package loadbalancer

import (
	"docker-lb/internal/config"
	"docker-lb/internal/database"
	"docker-lb/pkg/logger"
	"fmt"
	"sync"
	"time"
)

// Registry 镜像源信息
type Registry struct {
	Config           config.RegistryConfig
	Healthy          bool
	Disabled         bool      // 是否被禁用
	LastReviveCheck  time.Time // 上次复活检查时间
	Stats            RegistryStats
	Mu               sync.RWMutex
}

// IsHealthy 检查镜像源是否健康且可用
func (r *Registry) IsHealthy() bool {
	r.Mu.RLock()
	defer r.Mu.RUnlock()
	return r.Healthy && !r.Disabled
}

// IsAvailable 检查镜像源是否可用（健康且未禁用）
func (r *Registry) IsAvailable() bool {
	r.Mu.RLock()
	defer r.Mu.RUnlock()
	return r.Healthy && !r.Disabled && r.Config.Enabled
}

// RegistryStats 镜像源统计信息
type RegistryStats struct {
	TotalRequests       int64         `json:"total_requests"`
	SuccessRequests     int64         `json:"success_requests"`
	FailedRequests      int64         `json:"failed_requests"`
	AverageResponse     time.Duration `json:"average_response"`      // 请求平均响应时间
	HealthCheckResponse time.Duration `json:"health_check_response"` // 健康检查响应时间
	LastHealthCheck     time.Time     `json:"last_health_check"`
	ConsecutiveFails    int           `json:"consecutive_fails"`
}

// LoadBalancer 负载均衡器
type LoadBalancer struct {
	config      *config.Config
	registries  []*Registry
	currentIdx  int
	logger      *logger.Logger
	db          *database.Database
	Mu          sync.RWMutex
	healthCheck *HealthChecker
}

// New 创建新的负载均衡器
func New(cfg *config.Config, logger *logger.Logger, db *database.Database) *LoadBalancer {
	lb := &LoadBalancer{
		config: cfg,
		logger: logger,
		db:     db,
	}

	// 从数据库加载镜像源配置
	if err := lb.loadRegistriesFromDatabase(); err != nil {
		logger.Warn("Failed to load registries from database: %v", err)
		// 初始化空的镜像源列表
		lb.registries = []*Registry{}
	}

	// 创建健康检查器
	lb.healthCheck = NewHealthChecker(lb, cfg, logger, db)

	logger.Info("Load balancer initialized with %d registries", len(lb.registries))
	return lb
}

// GetNextRegistry 获取下一个可用的镜像源
func (lb *LoadBalancer) GetNextRegistry() (*Registry, error) {
	lb.Mu.RLock()
	defer lb.Mu.RUnlock()

	if len(lb.registries) == 0 {
		return nil, fmt.Errorf("no registries available")
	}

	switch lb.config.LoadBalancer.Strategy {
	case "round_robin":
		return lb.roundRobin()
	case "weighted_round_robin":
		return lb.weightedRoundRobin()
	case "least_connections":
		return lb.leastConnections()
	case "hash":
		return lb.hashBased()
	default:
		return lb.roundRobin()
	}
}

// roundRobin 轮询算法
func (lb *LoadBalancer) roundRobin() (*Registry, error) {
	healthyRegistries := lb.getHealthyRegistries()
	if len(healthyRegistries) == 0 {
		return nil, fmt.Errorf("no healthy registries available")
	}

	registry := healthyRegistries[lb.currentIdx%len(healthyRegistries)]
	lb.currentIdx++
	return registry, nil
}

// weightedRoundRobin 加权轮询算法
func (lb *LoadBalancer) weightedRoundRobin() (*Registry, error) {
	healthyRegistries := lb.getHealthyRegistries()
	if len(healthyRegistries) == 0 {
		return nil, fmt.Errorf("no healthy registries available")
	}

	// 计算总权重
	totalWeight := 0
	for _, reg := range healthyRegistries {
		totalWeight += reg.Config.Weight
	}

	// 根据权重选择
	target := lb.currentIdx % totalWeight
	currentWeight := 0
	
	for _, reg := range healthyRegistries {
		currentWeight += reg.Config.Weight
		if target < currentWeight {
			lb.currentIdx++
			return reg, nil
		}
	}

	// fallback
	lb.currentIdx++
	return healthyRegistries[0], nil
}

// leastConnections 最少连接算法
func (lb *LoadBalancer) leastConnections() (*Registry, error) {
	healthyRegistries := lb.getHealthyRegistries()
	if len(healthyRegistries) == 0 {
		return nil, fmt.Errorf("no healthy registries available")
	}

	// 简化实现：选择失败请求最少的
	var bestRegistry *Registry
	minFailed := int64(-1)

	for _, reg := range healthyRegistries {
		reg.Mu.RLock()
		failed := reg.Stats.FailedRequests
		reg.Mu.RUnlock()

		if minFailed == -1 || failed < minFailed {
			minFailed = failed
			bestRegistry = reg
		}
	}

	return bestRegistry, nil
}

// hashBased 基于哈希的算法
func (lb *LoadBalancer) hashBased() (*Registry, error) {
	healthyRegistries := lb.getHealthyRegistries()
	if len(healthyRegistries) == 0 {
		return nil, fmt.Errorf("no healthy registries available")
	}

	// 简化实现：基于时间戳的哈希
	hash := time.Now().UnixNano()
	index := hash % int64(len(healthyRegistries))
	return healthyRegistries[index], nil
}

// getHealthyRegistries 获取健康且可用的镜像源
func (lb *LoadBalancer) getHealthyRegistries() []*Registry {
	var healthy []*Registry
	for _, reg := range lb.registries {
		if reg.IsAvailable() {
			healthy = append(healthy, reg)
		}
	}
	return healthy
}

// GetRegistries 获取所有镜像源
func (lb *LoadBalancer) GetRegistries() []*Registry {
	lb.Mu.RLock()
	defer lb.Mu.RUnlock()
	
	result := make([]*Registry, len(lb.registries))
	copy(result, lb.registries)
	return result
}

// UpdateRegistryStats 更新镜像源统计
func (lb *LoadBalancer) UpdateRegistryStats(registry *Registry, success bool, responseTime time.Duration) {
	registry.Mu.Lock()
	defer registry.Mu.Unlock()

	registry.Stats.TotalRequests++
	if success {
		registry.Stats.SuccessRequests++
		registry.Stats.ConsecutiveFails = 0
	} else {
		registry.Stats.FailedRequests++
		registry.Stats.ConsecutiveFails++
	}

	// 更新平均响应时间（使用正确的加权平均算法）
	if registry.Stats.TotalRequests == 1 {
		registry.Stats.AverageResponse = responseTime
	} else {
		// 使用加权平均：(old_avg * (n-1) + new_value) / n
		oldAvg := registry.Stats.AverageResponse
		n := registry.Stats.TotalRequests
		registry.Stats.AverageResponse = (oldAvg*time.Duration(n-1) + responseTime) / time.Duration(n)
	}
}

// UpdateHealthCheckResponse 更新健康检查响应时间
func (lb *LoadBalancer) UpdateHealthCheckResponse(registry *Registry, responseTime time.Duration) {
	registry.Mu.Lock()
	defer registry.Mu.Unlock()

	registry.Stats.HealthCheckResponse = responseTime
}

// StartHealthCheck 启动健康检查
func (lb *LoadBalancer) StartHealthCheck() {
	lb.healthCheck.Start()
}

// StopHealthCheck 停止健康检查
func (lb *LoadBalancer) StopHealthCheck() {
	if lb.healthCheck != nil {
		lb.healthCheck.Stop()
	}
}

// GetHealthyRegistries 获取所有健康且可用的镜像源
func (lb *LoadBalancer) GetHealthyRegistries() []*Registry {
	lb.Mu.RLock()
	defer lb.Mu.RUnlock()

	var healthyRegistries []*Registry
	for _, registry := range lb.registries {
		if registry.IsAvailable() {
			healthyRegistries = append(healthyRegistries, registry)
		}
	}

	return healthyRegistries
}

// GetRegistryCount 获取可用镜像源数量
func (lb *LoadBalancer) GetRegistryCount() int {
	lb.Mu.RLock()
	defer lb.Mu.RUnlock()

	count := 0
	for _, registry := range lb.registries {
		if registry.Config.Enabled {
			count++
		}
	}

	return count
}

// loadRegistriesFromDatabase 从数据库加载镜像源配置
func (lb *LoadBalancer) loadRegistriesFromDatabase() error {
	if lb.db == nil {
		return fmt.Errorf("database not available")
	}

	configs, err := lb.db.GetAllRegistryConfigs()
	if err != nil {
		return fmt.Errorf("failed to get registry configs: %w", err)
	}

	if len(configs) == 0 {
		return fmt.Errorf("no registry configs found in database")
	}

	lb.Mu.Lock()
	defer lb.Mu.Unlock()

	// 清空现有镜像源
	lb.registries = nil

	// 从数据库配置创建镜像源
	for _, dbConfig := range configs {
		if dbConfig.Enabled {
			// 转换数据库配置为内部配置格式
			regConfig := config.RegistryConfig{
				Name:    dbConfig.Name,
				URL:     dbConfig.URL,
				Enabled: dbConfig.Enabled,
				Timeout: time.Duration(dbConfig.TimeoutSeconds) * time.Second,
				Weight:  dbConfig.Weight,
			}

			registry := &Registry{
				Config:  regConfig,
				Healthy: true, // 初始假设健康
				Stats:   RegistryStats{},
			}
			lb.registries = append(lb.registries, registry)
		}
	}

	lb.logger.Info("Loaded %d registries from database", len(lb.registries))
	return nil
}

// initializeFromConfig 从配置文件初始化镜像源并保存到数据库
func (lb *LoadBalancer) initializeFromConfig() {
	lb.Mu.Lock()
	defer lb.Mu.Unlock()

	// 清空现有镜像源
	lb.registries = nil

	// 从配置文件初始化镜像源
	for _, regCfg := range lb.config.Registries {
		if regCfg.Enabled {
			registry := &Registry{
				Config:  regCfg,
				Healthy: true, // 初始假设健康
				Stats:   RegistryStats{},
			}
			lb.registries = append(lb.registries, registry)

			// 保存到数据库
			if lb.db != nil {
				dbConfig := &database.RegistryConfig{
					Name:           regCfg.Name,
					URL:            regCfg.URL,
					Enabled:        regCfg.Enabled,
					TimeoutSeconds: int(regCfg.Timeout.Seconds()),
					Weight:         regCfg.Weight,
					Description:    fmt.Sprintf("Imported from config file"),
				}

				if err := lb.db.CreateRegistryConfig(dbConfig); err != nil {
					lb.logger.Warn("Failed to save registry config to database: %s - %v", regCfg.Name, err)
				} else {
					lb.logger.Info("Saved registry config to database: %s", regCfg.Name)
				}
			}
		}
	}

	lb.logger.Info("Initialized %d registries from config file", len(lb.registries))
}

// RefreshRegistries 刷新镜像源列表（从数据库重新加载）
func (lb *LoadBalancer) RefreshRegistries() error {
	if err := lb.loadRegistriesFromDatabase(); err != nil {
		return fmt.Errorf("failed to refresh registries: %w", err)
	}

	// 注意：不重新启动健康检查器，让它继续使用新的镜像源列表
	// 健康检查器会在下次检查时自动发现新的镜像源
	lb.logger.Debug("Registries refreshed, health checker will discover changes on next check")

	return nil
}

// AddRegistry 添加新的镜像源
func (lb *LoadBalancer) AddRegistry(dbConfig *database.RegistryConfig) error {
	if lb.db == nil {
		return fmt.Errorf("database not available")
	}

	// 保存到数据库
	if err := lb.db.CreateRegistryConfig(dbConfig); err != nil {
		return fmt.Errorf("failed to create registry config: %w", err)
	}

	// 刷新镜像源列表
	return lb.RefreshRegistries()
}

// UpdateRegistry 更新镜像源
func (lb *LoadBalancer) UpdateRegistry(dbConfig *database.RegistryConfig) error {
	if lb.db == nil {
		return fmt.Errorf("database not available")
	}

	// 更新数据库
	if err := lb.db.UpdateRegistryConfig(dbConfig); err != nil {
		return fmt.Errorf("failed to update registry config: %w", err)
	}

	// 刷新镜像源列表
	return lb.RefreshRegistries()
}

// RemoveRegistry 删除镜像源
func (lb *LoadBalancer) RemoveRegistry(id int) error {
	if lb.db == nil {
		return fmt.Errorf("database not available")
	}

	// 从数据库删除
	if err := lb.db.DeleteRegistryConfig(id); err != nil {
		return fmt.Errorf("failed to delete registry config: %w", err)
	}

	// 刷新镜像源列表
	return lb.RefreshRegistries()
}

// RemoveRegistryByName 根据名称删除镜像源
func (lb *LoadBalancer) RemoveRegistryByName(name string) error {
	if lb.db == nil {
		return fmt.Errorf("database not available")
	}

	// 从数据库删除
	if err := lb.db.DeleteRegistryConfigByName(name); err != nil {
		return fmt.Errorf("failed to delete registry config by name: %w", err)
	}

	// 刷新镜像源列表
	return lb.RefreshRegistries()
}
