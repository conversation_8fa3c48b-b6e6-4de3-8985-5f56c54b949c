package monitoring

import (
	"docker-lb/internal/auth"
	"docker-lb/internal/database"
	"docker-lb/pkg/logger"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// Monitor 监控管理器
type Monitor struct {
	db     *database.Database
	auth   *auth.AuthManager
	logger *logger.Logger
}

// DashboardStats 仪表板统计
type DashboardStats struct {
	TotalRequests    int64                    `json:"total_requests"`
	TotalBytesIn     int64                    `json:"total_bytes_in"`
	TotalBytesOut    int64                    `json:"total_bytes_out"`
	SuccessRate      float64                  `json:"success_rate"`
	TopIPs           []database.IPStats       `json:"top_ips"`
	HourlyStats      []HourlyStats            `json:"hourly_stats"`
	RegistryStats    []RegistryStats          `json:"registry_stats"`
	RecentRequests   []database.RequestLog    `json:"recent_requests"`
}

// HourlyStats 小时统计
type HourlyStats struct {
	Hour         string `json:"hour"`
	Requests     int64  `json:"requests"`
	BytesIn      int64  `json:"bytes_in"`
	BytesOut     int64  `json:"bytes_out"`
	SuccessRate  float64 `json:"success_rate"`
}

// RegistryStats 镜像源统计
type RegistryStats struct {
	Name            string  `json:"name"`
	Requests        int64   `json:"requests"`
	SuccessRate     float64 `json:"success_rate"`
	AvgResponseTime int64   `json:"avg_response_time"`
	BytesTransfer   int64   `json:"bytes_transfer"`
}

// New 创建监控管理器
func New(db *database.Database, auth *auth.AuthManager, logger *logger.Logger) *Monitor {
	return &Monitor{
		db:     db,
		auth:   auth,
		logger: logger,
	}
}

// SetupRoutes 设置监控路由
func (m *Monitor) SetupRoutes(router *gin.RouterGroup) {
	// 认证路由
	router.POST("/auth/login", m.auth.LoginHandler)
	
	// 需要认证的路由
	protected := router.Group("/")
	protected.Use(m.auth.AuthMiddleware())
	{
		// 用户信息
		protected.GET("/auth/user", m.auth.GetUserInfoHandler)
		protected.POST("/auth/refresh", m.auth.RefreshTokenHandler)
		protected.POST("/auth/change-password", m.auth.ChangePasswordHandler)
		
		// 监控统计
		protected.GET("/monitoring/dashboard", m.GetDashboardStats)
		protected.GET("/monitoring/traffic", m.GetTrafficStats)
		protected.GET("/monitoring/traffic/hourly", m.GetHourlyTrafficStats)
		protected.GET("/monitoring/traffic/daily", m.GetDailyTrafficSummary)
		protected.GET("/monitoring/traffic/registry", m.GetRegistryTrafficStats)
		protected.GET("/monitoring/ips", m.GetTopIPs)
		protected.GET("/monitoring/ips/traffic", m.GetTopTrafficIPs)
		protected.GET("/monitoring/requests", m.GetRecentRequests)
		protected.GET("/monitoring/export", m.ExportData)
		
		// 系统管理
		protected.GET("/monitoring/system", m.GetSystemInfo)
		protected.POST("/monitoring/cleanup", m.CleanupOldData)
	}
}

// GetDashboardStats 获取仪表板统计
func (m *Monitor) GetDashboardStats(c *gin.Context) {
	hours := 24 // 默认24小时
	if h := c.Query("hours"); h != "" {
		if parsed, err := strconv.Atoi(h); err == nil && parsed > 0 && parsed <= 168 { // 最多7天
			hours = parsed
		}
	}

	// 使用默认值初始化统计数据
	stats := DashboardStats{
		TotalRequests:  0,
		TotalBytesIn:   0,
		TotalBytesOut:  0,
		SuccessRate:    0,
		TopIPs:         []database.IPStats{},
		HourlyStats:    []HourlyStats{},
		RegistryStats:  []RegistryStats{},
		RecentRequests: []database.RequestLog{},
	}

	// 获取基础流量统计（必需的）
	if trafficStats, err := m.db.GetTrafficStats(hours); err != nil {
		m.logger.Error("Failed to get traffic stats: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get traffic stats"})
		return
	} else {
		stats.TotalRequests = trafficStats.TotalRequests
		stats.TotalBytesIn = trafficStats.TotalBytesIn
		stats.TotalBytesOut = trafficStats.TotalBytesOut
		stats.SuccessRate = trafficStats.SuccessRate
	}

	// 获取其他统计数据（可选的，失败时使用默认值）
	if topIPs, err := m.db.GetTopIPs(10); err == nil {
		stats.TopIPs = topIPs
	} else {
		m.logger.Warn("Failed to get top IPs: %v", err)
	}

	if hourlyStats, err := m.getHourlyStats(hours); err == nil {
		stats.HourlyStats = hourlyStats
	} else {
		m.logger.Warn("Failed to get hourly stats: %v", err)
	}

	if registryStats, err := m.getRegistryStats(hours); err == nil {
		stats.RegistryStats = registryStats
	} else {
		m.logger.Warn("Failed to get registry stats: %v", err)
	}

	if recentRequests, err := m.getRecentRequests(20); err == nil { // 减少数量
		stats.RecentRequests = recentRequests
	} else {
		m.logger.Warn("Failed to get recent requests: %v", err)
	}

	c.JSON(http.StatusOK, stats)
}

// GetTrafficStats 获取流量统计
func (m *Monitor) GetTrafficStats(c *gin.Context) {
	hours := 24
	if h := c.Query("hours"); h != "" {
		if parsed, err := strconv.Atoi(h); err == nil && parsed > 0 {
			hours = parsed
		}
	}

	stats, err := m.db.GetTrafficStats(hours)
	if err != nil {
		m.logger.Error("Failed to get traffic stats: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get traffic stats"})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetTopIPs 获取Top IP
func (m *Monitor) GetTopIPs(c *gin.Context) {
	limit := 20
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	ips, err := m.db.GetTopIPs(limit)
	if err != nil {
		m.logger.Error("Failed to get top IPs: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get top IPs"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"ips": ips,
		"total": len(ips),
	})
}

// GetRecentRequests 获取最近请求
func (m *Monitor) GetRecentRequests(c *gin.Context) {
	limit := 100
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 1000 {
			limit = parsed
		}
	}

	requests, err := m.getRecentRequests(limit)
	if err != nil {
		m.logger.Error("Failed to get recent requests: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get recent requests"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"requests": requests,
		"total": len(requests),
	})
}

// getHourlyStats 获取小时统计
func (m *Monitor) getHourlyStats(hours int) ([]HourlyStats, error) {
	// 这里应该从数据库获取小时聚合数据
	// 为了简化，我们返回空数组
	return []HourlyStats{}, nil
}

// getRegistryStats 获取镜像源统计
func (m *Monitor) getRegistryStats(hours int) ([]RegistryStats, error) {
	// 这里应该从数据库获取镜像源统计
	// 为了简化，我们返回空数组
	return []RegistryStats{}, nil
}

// getRecentRequests 获取最近请求
func (m *Monitor) getRecentRequests(limit int) ([]database.RequestLog, error) {
	// 这里应该从数据库获取最近请求
	// 为了简化，我们返回空数组
	return []database.RequestLog{}, nil
}

// ExportData 导出数据
func (m *Monitor) ExportData(c *gin.Context) {
	format := c.Query("format")
	if format == "" {
		format = "json"
	}

	hours := 24
	if h := c.Query("hours"); h != "" {
		if parsed, err := strconv.Atoi(h); err == nil && parsed > 0 {
			hours = parsed
		}
	}

	switch format {
	case "csv":
		m.exportCSV(c, hours)
	case "json":
		m.exportJSON(c, hours)
	default:
		c.JSON(http.StatusBadRequest, gin.H{"error": "Unsupported format"})
	}
}

// exportJSON 导出JSON格式
func (m *Monitor) exportJSON(c *gin.Context, hours int) {
	stats, err := m.db.GetTrafficStats(hours)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to export data"})
		return
	}

	c.Header("Content-Disposition", "attachment; filename=docker-lb-stats.json")
	c.JSON(http.StatusOK, stats)
}

// exportCSV 导出CSV格式
func (m *Monitor) exportCSV(c *gin.Context, hours int) {
	c.Header("Content-Type", "text/csv")
	c.Header("Content-Disposition", "attachment; filename=docker-lb-stats.csv")
	
	// 简化的CSV导出
	c.String(http.StatusOK, "timestamp,requests,bytes_in,bytes_out,success_rate\n")
	c.String(http.StatusOK, "%s,0,0,0,0.0\n", time.Now().Format("2006-01-02 15:04:05"))
}

// GetSystemInfo 获取系统信息
func (m *Monitor) GetSystemInfo(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"version": "1.0.0",
		"uptime": time.Since(time.Now()).String(),
		"database": "sqlite",
		"auth_enabled": true,
	})
}

// GetHourlyTrafficStats 获取小时流量统计
func (m *Monitor) GetHourlyTrafficStats(c *gin.Context) {
	hours := 24 // 默认24小时
	if h := c.Query("hours"); h != "" {
		if parsed, err := strconv.Atoi(h); err == nil && parsed > 0 {
			hours = parsed
		}
	}

	stats, err := m.db.GetHourlyTrafficStats(hours)
	if err != nil {
		m.logger.Error("Failed to get hourly traffic stats: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get hourly traffic stats"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"stats": stats,
		"total": len(stats),
	})
}

// GetDailyTrafficSummary 获取日流量汇总
func (m *Monitor) GetDailyTrafficSummary(c *gin.Context) {
	days := 7 // 默认7天
	if d := c.Query("days"); d != "" {
		if parsed, err := strconv.Atoi(d); err == nil && parsed > 0 {
			days = parsed
		}
	}

	stats, err := m.db.GetDailyTrafficSummary(days)
	if err != nil {
		m.logger.Error("Failed to get daily traffic summary: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get daily traffic summary"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"stats": stats,
		"total": len(stats),
	})
}

// GetRegistryTrafficStats 获取镜像源流量统计
func (m *Monitor) GetRegistryTrafficStats(c *gin.Context) {
	hours := 24 // 默认24小时
	if h := c.Query("hours"); h != "" {
		if parsed, err := strconv.Atoi(h); err == nil && parsed > 0 {
			hours = parsed
		}
	}

	stats, err := m.db.GetRegistryTrafficStats(hours)
	if err != nil {
		m.logger.Error("Failed to get registry traffic stats: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get registry traffic stats"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"stats": stats,
		"total": len(stats),
	})
}

// GetTopTrafficIPs 获取流量消耗最多的IP
func (m *Monitor) GetTopTrafficIPs(c *gin.Context) {
	limit := 20
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	ips, err := m.db.GetTopTrafficIPs(limit)
	if err != nil {
		m.logger.Error("Failed to get top traffic IPs: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get top traffic IPs"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"ips": ips,
		"total": len(ips),
	})
}

// CleanupOldData 清理旧数据
func (m *Monitor) CleanupOldData(c *gin.Context) {
	days := 30 // 默认保留30天
	if d := c.Query("days"); d != "" {
		if parsed, err := strconv.Atoi(d); err == nil && parsed > 0 {
			days = parsed
		}
	}

	m.logger.Info("Cleanup old data older than %d days", days)

	// 这里应该实现实际的清理逻辑
	c.JSON(http.StatusOK, gin.H{
		"message": "Cleanup completed",
		"days": days,
	})
}
