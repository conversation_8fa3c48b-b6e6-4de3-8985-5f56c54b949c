package cache

import (
	"encoding/json"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"sync/atomic"
	"time"
)

// CachedResponse 缓存的响应
type CachedResponse struct {
	Headers    map[string][]string `json:"headers"`
	StatusCode int                 `json:"status_code"`
	Body       []byte              `json:"-"` // 不序列化到JSON
	CreatedAt  time.Time           `json:"created_at"`
	Size       int64               `json:"size"`
}

// SetWithResponse 缓存HTTP响应
func (c *Cache) SetWithResponse(url, path string, resp *http.Response, body []byte) error {
	if !c.config.Enabled {
		return nil
	}

	// 检查文件大小限制
	if int64(len(body)) > c.maxFileSize {
		c.logger.Debug("File too large for cache: %d bytes", len(body))
		return nil
	}

	key := c.generateKey(url, path)
	filePath := filepath.Join(c.storagePath, key)
	metaPath := filePath + ".meta"
	shard := c.getShard(key)

	// 检查缓存空间
	totalSize := c.getTotalSize()
	if totalSize+int64(len(body)) > c.maxSize {
		if err := c.ensureSpace(int64(len(body))); err != nil {
			c.logger.Warn("Failed to ensure cache space: %v", err)
			return err
		}
	}

	// 创建缓存响应
	cachedResp := &CachedResponse{
		Headers:    make(map[string][]string),
		StatusCode: resp.StatusCode,
		CreatedAt:  time.Now(),
		Size:       int64(len(body)),
	}

	// 复制重要的响应头
	importantHeaders := []string{
		"Content-Type",
		"Content-Length",
		"Docker-Content-Digest",
		"Docker-Distribution-API-Version",
		"ETag",
		"Last-Modified",
	}

	for _, header := range importantHeaders {
		if values := resp.Header[header]; len(values) > 0 {
			cachedResp.Headers[header] = values
		}
	}

	// 保存元数据
	metaData, err := json.Marshal(cachedResp)
	if err != nil {
		return err
	}

	if err := os.WriteFile(metaPath, metaData, 0644); err != nil {
		return err
	}

	// 保存响应体
	if err := os.WriteFile(filePath, body, 0644); err != nil {
		os.Remove(metaPath) // 清理元数据文件
		return err
	}

	// 更新分片统计
	shard.mu.Lock()
	atomic.AddInt64(&shard.totalSize, int64(len(body)))
	atomic.AddInt64(&shard.fileCount, 1)
	shard.mu.Unlock()

	c.logger.Debug("Cached response with metadata: %s (%d bytes)", key, len(body))
	return nil
}

// GetWithResponse 获取缓存的响应
func (c *Cache) GetWithResponse(url, path string) (*CachedResponse, io.ReadCloser, bool) {
	if !c.config.Enabled {
		return nil, nil, false
	}

	key := c.generateKey(url, path)
	filePath := filepath.Join(c.storagePath, key)
	metaPath := filePath + ".meta"
	shard := c.getShard(key)

	// 使用读锁进行快速检查
	shard.mu.RLock()

	// 检查文件是否存在
	info, err := os.Stat(filePath)
	if err != nil {
		shard.mu.RUnlock()
		atomic.AddInt64(&c.missCount, 1)
		return nil, nil, false
	}

	// 检查元数据文件
	metaInfo, err := os.Stat(metaPath)
	if err != nil {
		shard.mu.RUnlock()
		atomic.AddInt64(&c.missCount, 1)
		return nil, nil, false
	}

	// 检查是否过期
	if time.Since(info.ModTime()) > c.expireTime || time.Since(metaInfo.ModTime()) > c.expireTime {
		shard.mu.RUnlock()
		// 异步删除过期文件，避免阻塞
		go func() {
			shard.mu.Lock()
			defer shard.mu.Unlock()
			os.Remove(filePath)
			os.Remove(metaPath)
			atomic.AddInt64(&shard.totalSize, -info.Size())
			atomic.AddInt64(&shard.fileCount, -1)
		}()
		atomic.AddInt64(&c.missCount, 1)
		return nil, nil, false
	}

	// 读取元数据
	metaData, err := os.ReadFile(metaPath)
	shard.mu.RUnlock() // 释放读锁

	if err != nil {
		c.logger.Error("Failed to read cache metadata: %v", err)
		atomic.AddInt64(&c.missCount, 1)
		return nil, nil, false
	}

	var cachedResp CachedResponse
	if err := json.Unmarshal(metaData, &cachedResp); err != nil {
		c.logger.Error("Failed to unmarshal cache metadata: %v", err)
		atomic.AddInt64(&c.missCount, 1)
		return nil, nil, false
	}

	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		c.logger.Error("Failed to open cache file: %v", err)
		atomic.AddInt64(&c.missCount, 1)
		return nil, nil, false
	}

	// 异步更新访问时间，不阻塞读取
	go func() {
		os.Chtimes(filePath, time.Now(), info.ModTime())
		os.Chtimes(metaPath, time.Now(), metaInfo.ModTime())
	}()

	atomic.AddInt64(&c.hitCount, 1)
	c.logger.Debug("Cache hit with metadata: %s", key)

	return &cachedResp, file, true
}

// ApplyHeaders 应用缓存的响应头到HTTP响应
func (cr *CachedResponse) ApplyHeaders(header http.Header) {
	for key, values := range cr.Headers {
		for _, value := range values {
			header.Add(key, value)
		}
	}
}
