package cache

import (
	"bytes"
	"io"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// CacheMiddleware 缓存中间件
func (c *Cache) CacheMiddleware() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if !c.config.Enabled {
			ctx.Next()
			return
		}

		// 只缓存Docker相关请求
		if !strings.HasPrefix(ctx.Request.URL.Path, "/v2/") {
			ctx.Next()
			return
		}

		// 只缓存GET和HEAD请求
		if ctx.Request.Method != "GET" && ctx.Request.Method != "HEAD" {
			ctx.Next()
			return
		}

		// 检查是否应该缓存此类型的请求
		if !c.shouldCache(ctx.Request.URL.Path) {
			ctx.Next()
			return
		}

		// 尝试从缓存获取
		targetURL := ctx.GetString("target_url")
		if targetURL == "" {
			ctx.Next()
			return
		}

		if reader, found := c.Get(targetURL, ctx.Request.URL.Path); found {
			c.serveCachedResponse(ctx, reader)
			return
		}

		// 缓存未命中，继续处理请求并缓存响应
		c.cacheResponse(ctx, targetURL)
	}
}

// shouldCache 判断是否应该缓存
func (c *Cache) shouldCache(path string) bool {
	// Manifest缓存
	if c.config.EnableManifestCache && (strings.Contains(path, "/manifests/") || strings.Contains(path, "/tags/")) {
		return true
	}

	// Blob缓存
	if c.config.EnableBlobCache && strings.Contains(path, "/blobs/") {
		return true
	}

	return false
}

// serveCachedResponse 提供缓存的响应
func (c *Cache) serveCachedResponse(ctx *gin.Context, reader io.ReadCloser) {
	defer reader.Close()

	// 设置响应头
	ctx.Header("X-Cache", "HIT")
	ctx.Header("Content-Type", "application/octet-stream")

	// 如果是HEAD请求，只返回头部
	if ctx.Request.Method == "HEAD" {
		ctx.Status(http.StatusOK)
		return
	}

	// 复制缓存内容到响应
	ctx.Status(http.StatusOK)
	io.Copy(ctx.Writer, reader)
}

// cacheResponse 缓存响应
func (c *Cache) cacheResponse(ctx *gin.Context, targetURL string) {
	// 创建响应写入器包装器
	wrapper := &responseWrapper{
		ResponseWriter: ctx.Writer,
		body:          &bytes.Buffer{},
		cache:         c,
		targetURL:     targetURL,
		path:          ctx.Request.URL.Path,
	}

	ctx.Writer = wrapper
	ctx.Next()

	// 请求处理完成后，尝试缓存响应
	wrapper.finalize()
}

// responseWrapper 响应包装器
type responseWrapper struct {
	gin.ResponseWriter
	body      *bytes.Buffer
	cache     *Cache
	targetURL string
	path      string
	written   bool
}

// Write 写入响应数据
func (w *responseWrapper) Write(data []byte) (int, error) {
	// 写入到原始响应
	n, err := w.ResponseWriter.Write(data)
	if err != nil {
		return n, err
	}

	// 同时写入到缓冲区用于缓存
	if w.body != nil && w.shouldCacheResponse() {
		w.body.Write(data[:n])
	}

	return n, err
}

// WriteHeader 写入响应头
func (w *responseWrapper) WriteHeader(statusCode int) {
	w.ResponseWriter.WriteHeader(statusCode)
}

// shouldCacheResponse 判断是否应该缓存响应
func (w *responseWrapper) shouldCacheResponse() bool {
	// 只缓存成功的响应
	status := w.Status()
	if status < 200 || status >= 300 {
		return false
	}

	// 检查Content-Type
	contentType := w.Header().Get("Content-Type")
	if contentType != "" && !strings.Contains(contentType, "application/") {
		return false
	}

	return true
}

// finalize 完成缓存操作
func (w *responseWrapper) finalize() {
	if w.body == nil || w.body.Len() == 0 || !w.shouldCacheResponse() {
		return
	}

	// 获取内容长度
	contentLength := int64(w.body.Len())
	if contentLengthStr := w.Header().Get("Content-Length"); contentLengthStr != "" {
		if parsed, err := strconv.ParseInt(contentLengthStr, 10, 64); err == nil {
			contentLength = parsed
		}
	}

	// 缓存响应
	reader := bytes.NewReader(w.body.Bytes())
	if err := w.cache.Set(w.targetURL, w.path, reader, contentLength); err != nil {
		w.cache.logger.Warn("Failed to cache response: %v", err)
	} else {
		w.cache.logger.Debug("Cached response: %s%s (%d bytes)", w.targetURL, w.path, contentLength)
	}
}

// CacheStatsHandler 缓存统计处理器
func (c *Cache) CacheStatsHandler() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		stats := c.GetStats()
		
		// 计算命中率
		totalRequests := stats.HitCount + stats.MissCount
		hitRate := float64(0)
		if totalRequests > 0 {
			hitRate = float64(stats.HitCount) / float64(totalRequests) * 100
		}

		response := gin.H{
			"enabled":        c.config.Enabled,
			"total_size":     stats.TotalSize,
			"file_count":     stats.FileCount,
			"hit_count":      stats.HitCount,
			"miss_count":     stats.MissCount,
			"eviction_count": stats.EvictionCount,
			"hit_rate":       hitRate,
			"max_size":       c.maxSize,
			"storage_path":   c.storagePath,
			"expire_time":    c.config.ExpireTime,
		}

		ctx.JSON(http.StatusOK, response)
	}
}

// CacheClearHandler 清空缓存处理器
func (c *Cache) CacheClearHandler() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if err := c.Clear(); err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to clear cache",
			})
			return
		}

		ctx.JSON(http.StatusOK, gin.H{
			"message": "Cache cleared successfully",
		})
	}
}

// TeeReader 用于同时读取和缓存数据
type TeeReader struct {
	reader io.Reader
	cache  *Cache
	url    string
	path   string
	size   int64
	buffer *bytes.Buffer
}

// NewTeeReader 创建TeeReader
func (c *Cache) NewTeeReader(reader io.Reader, url, path string, size int64) *TeeReader {
	return &TeeReader{
		reader: reader,
		cache:  c,
		url:    url,
		path:   path,
		size:   size,
		buffer: &bytes.Buffer{},
	}
}

// Read 读取数据
func (tr *TeeReader) Read(p []byte) (n int, err error) {
	n, err = tr.reader.Read(p)
	if n > 0 {
		tr.buffer.Write(p[:n])
	}
	
	// 如果读取完成，缓存数据
	if err == io.EOF && tr.buffer.Len() > 0 {
		go func() {
			reader := bytes.NewReader(tr.buffer.Bytes())
			if cacheErr := tr.cache.Set(tr.url, tr.path, reader, int64(tr.buffer.Len())); cacheErr != nil {
				tr.cache.logger.Warn("Failed to cache data: %v", cacheErr)
			}
		}()
	}
	
	return n, err
}

// Close 关闭读取器
func (tr *TeeReader) Close() error {
	if closer, ok := tr.reader.(io.Closer); ok {
		return closer.Close()
	}
	return nil
}
