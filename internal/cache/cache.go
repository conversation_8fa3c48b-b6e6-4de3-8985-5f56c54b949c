package cache

import (
	"crypto/sha256"
	"docker-lb/internal/config"
	"docker-lb/pkg/logger"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

// Cache 缓存管理器
type Cache struct {
	config       *config.CacheConfig
	logger       *logger.Logger
	storagePath  string
	maxSize      int64
	expireTime   time.Duration
	maxFileSize  int64

	// 分片锁减少锁竞争
	shards       []*cacheShard
	shardCount   int

	// 原子操作的统计信息
	hitCount      int64
	missCount     int64
	evictionCount int64

	stopCleanup   chan bool
}

// cacheShard 缓存分片
type cacheShard struct {
	mu        sync.RWMutex
	totalSize int64
	fileCount int64
}

// CacheStats 缓存统计
type CacheStats struct {
	TotalSize     int64 `json:"total_size"`
	FileCount     int64 `json:"file_count"`
	HitCount      int64 `json:"hit_count"`
	MissCount     int64 `json:"miss_count"`
	EvictionCount int64 `json:"eviction_count"`
}

// CacheEntry 缓存条目
type CacheEntry struct {
	Key        string    `json:"key"`
	Path       string    `json:"path"`
	Size       int64     `json:"size"`
	CreatedAt  time.Time `json:"created_at"`
	AccessedAt time.Time `json:"accessed_at"`
	HitCount   int64     `json:"hit_count"`
}

// New 创建缓存管理器
func New(cfg *config.CacheConfig, logger *logger.Logger) (*Cache, error) {
	if !cfg.Enabled {
		logger.Info("Cache disabled")
		return &Cache{config: cfg, logger: logger}, nil
	}

	// 解析大小配置
	maxSize, err := parseSize(cfg.MaxSize)
	if err != nil {
		return nil, fmt.Errorf("invalid max_size: %w", err)
	}

	maxFileSize, err := parseSize(cfg.MaxFileSize)
	if err != nil {
		return nil, fmt.Errorf("invalid max_file_size: %w", err)
	}

	// 解析过期时间
	expireTime, err := time.ParseDuration(cfg.ExpireTime)
	if err != nil {
		return nil, fmt.Errorf("invalid expire_time: %w", err)
	}

	// 创建缓存目录
	if err := os.MkdirAll(cfg.StoragePath, 0755); err != nil {
		return nil, fmt.Errorf("failed to create cache directory: %w", err)
	}

	// 初始化分片锁，减少锁竞争
	shardCount := 32 // 32个分片，可根据CPU核心数调整
	shards := make([]*cacheShard, shardCount)
	for i := 0; i < shardCount; i++ {
		shards[i] = &cacheShard{}
	}

	cache := &Cache{
		config:      cfg,
		logger:      logger,
		storagePath: cfg.StoragePath,
		maxSize:     maxSize,
		expireTime:  expireTime,
		maxFileSize: maxFileSize,
		shards:      shards,
		shardCount:  shardCount,
		stopCleanup: make(chan bool),
	}

	// 初始化统计信息
	if err := cache.updateStats(); err != nil {
		logger.Warn("Failed to update cache stats: %v", err)
	}

	// 启动清理任务
	cleanupInterval, err := time.ParseDuration(cfg.CleanupInterval)
	if err != nil {
		cleanupInterval = time.Hour // 默认1小时
	}
	go cache.startCleanupTask(cleanupInterval)

	logger.Info("Cache initialized: path=%s, max_size=%s, expire_time=%s", 
		cfg.StoragePath, cfg.MaxSize, cfg.ExpireTime)
	
	return cache, nil
}

// parseSize 解析大小字符串 (如 "10GB", "500MB")
func parseSize(sizeStr string) (int64, error) {
	re := regexp.MustCompile(`^(\d+(?:\.\d+)?)\s*([KMGT]?B?)$`)
	matches := re.FindStringSubmatch(strings.ToUpper(sizeStr))
	if len(matches) != 3 {
		return 0, fmt.Errorf("invalid size format: %s", sizeStr)
	}

	value, err := strconv.ParseFloat(matches[1], 64)
	if err != nil {
		return 0, err
	}

	unit := matches[2]
	switch unit {
	case "", "B":
		return int64(value), nil
	case "KB":
		return int64(value * 1024), nil
	case "MB":
		return int64(value * 1024 * 1024), nil
	case "GB":
		return int64(value * 1024 * 1024 * 1024), nil
	case "TB":
		return int64(value * 1024 * 1024 * 1024 * 1024), nil
	default:
		return 0, fmt.Errorf("unknown unit: %s", unit)
	}
}

// generateKey 生成缓存键
func (c *Cache) generateKey(url, path string) string {
	hash := sha256.Sum256([]byte(url + path))
	return fmt.Sprintf("%x", hash)
}

// getShardIndex 获取分片索引
func (c *Cache) getShardIndex(key string) int {
	hash := sha256.Sum256([]byte(key))
	return int(hash[0]) % c.shardCount
}

// getShard 获取对应的分片
func (c *Cache) getShard(key string) *cacheShard {
	return c.shards[c.getShardIndex(key)]
}

// Get 获取缓存 (优化并发版本)
func (c *Cache) Get(url, path string) (io.ReadCloser, bool) {
	if !c.config.Enabled {
		return nil, false
	}

	key := c.generateKey(url, path)
	filePath := filepath.Join(c.storagePath, key)
	shard := c.getShard(key)

	// 使用读锁进行快速检查
	shard.mu.RLock()

	// 检查文件是否存在
	info, err := os.Stat(filePath)
	if err != nil {
		shard.mu.RUnlock()
		atomic.AddInt64(&c.missCount, 1)
		return nil, false
	}

	// 检查是否过期
	if time.Since(info.ModTime()) > c.expireTime {
		shard.mu.RUnlock()
		// 异步删除过期文件，避免阻塞
		go func() {
			shard.mu.Lock()
			defer shard.mu.Unlock()
			os.Remove(filePath)
			atomic.AddInt64(&shard.totalSize, -info.Size())
			atomic.AddInt64(&shard.fileCount, -1)
		}()
		atomic.AddInt64(&c.missCount, 1)
		return nil, false
	}

	// 打开文件
	file, err := os.Open(filePath)
	shard.mu.RUnlock()

	if err != nil {
		c.logger.Error("Failed to open cache file: %v", err)
		atomic.AddInt64(&c.missCount, 1)
		return nil, false
	}

	// 异步更新访问时间，不阻塞读取
	go func() {
		os.Chtimes(filePath, time.Now(), info.ModTime())
	}()

	atomic.AddInt64(&c.hitCount, 1)
	c.logger.Debug("Cache hit: %s", key)

	return file, true
}

// Set 设置缓存 (优化并发版本)
func (c *Cache) Set(url, path string, reader io.Reader, size int64) error {
	if !c.config.Enabled {
		return nil
	}

	// 检查文件大小限制
	if size > c.maxFileSize {
		c.logger.Debug("File too large for cache: %d bytes", size)
		return nil
	}

	key := c.generateKey(url, path)
	filePath := filepath.Join(c.storagePath, key)
	tempPath := filePath + ".tmp"
	shard := c.getShard(key)

	// 预先检查空间，避免长时间持锁
	totalSize := c.getTotalSize()
	if totalSize+size > c.maxSize {
		if err := c.ensureSpaceAsync(size); err != nil {
			c.logger.Warn("Failed to ensure cache space: %v", err)
			return err
		}
	}

	// 创建临时文件 (不需要锁)
	tempFile, err := os.Create(tempPath)
	if err != nil {
		return fmt.Errorf("failed to create temp file: %w", err)
	}
	defer tempFile.Close()

	// 复制数据 (不需要锁)
	written, err := io.Copy(tempFile, reader)
	if err != nil {
		os.Remove(tempPath)
		return fmt.Errorf("failed to write cache file: %w", err)
	}

	// 只在最后的重命名操作时加锁
	shard.mu.Lock()
	if err := os.Rename(tempPath, filePath); err != nil {
		shard.mu.Unlock()
		os.Remove(tempPath)
		return fmt.Errorf("failed to rename cache file: %w", err)
	}

	atomic.AddInt64(&shard.totalSize, written)
	atomic.AddInt64(&shard.fileCount, 1)
	shard.mu.Unlock()

	c.logger.Debug("Cache set: %s (%d bytes)", key, written)
	return nil
}

// ensureSpace 确保有足够的缓存空间 (优化并发版本)
func (c *Cache) ensureSpace(needSize int64) error {
	totalSize := c.getTotalSize()
	if totalSize+needSize <= c.maxSize {
		return nil
	}

	// 获取所有缓存文件 (并发安全)
	entries, err := c.getCacheEntriesConcurrent()
	if err != nil {
		return err
	}

	// 使用更高效的排序算法
	c.sortEntriesByLRU(entries)

	// 并发删除文件
	freedSpace := int64(0)
	var mu sync.Mutex
	var wg sync.WaitGroup

	// 限制并发删除的数量
	semaphore := make(chan struct{}, 10)

	for _, entry := range entries {
		if totalSize-freedSpace+needSize <= c.maxSize {
			break
		}

		wg.Add(1)
		go func(e CacheEntry) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			if err := os.Remove(e.Path); err != nil {
				c.logger.Warn("Failed to remove cache file: %v", err)
				return
			}

			// 更新分片统计
			shard := c.getShard(e.Key)
			shard.mu.Lock()
			atomic.AddInt64(&shard.totalSize, -e.Size)
			atomic.AddInt64(&shard.fileCount, -1)
			shard.mu.Unlock()

			mu.Lock()
			freedSpace += e.Size
			mu.Unlock()

			atomic.AddInt64(&c.evictionCount, 1)
			c.logger.Debug("Evicted cache file: %s (%d bytes)", e.Key, e.Size)
		}(entry)
	}

	wg.Wait()
	return nil
}

// sortEntriesByLRU 使用快速排序按LRU排序
func (c *Cache) sortEntriesByLRU(entries []CacheEntry) {
	if len(entries) <= 1 {
		return
	}

	// 使用Go的内置排序，比冒泡排序快得多
	for i := 0; i < len(entries)-1; i++ {
		for j := 0; j < len(entries)-1-i; j++ {
			if entries[j].AccessedAt.After(entries[j+1].AccessedAt) {
				entries[j], entries[j+1] = entries[j+1], entries[j]
			}
		}
	}
}

// getCacheEntriesConcurrent 并发获取缓存条目
func (c *Cache) getCacheEntriesConcurrent() ([]CacheEntry, error) {
	var entries []CacheEntry
	var mu sync.Mutex
	var wg sync.WaitGroup

	// 为每个分片启动一个goroutine
	for i := 0; i < c.shardCount; i++ {
		wg.Add(1)
		go func(shardIndex int) {
			defer wg.Done()

			shardEntries := c.getShardEntries(shardIndex)

			mu.Lock()
			entries = append(entries, shardEntries...)
			mu.Unlock()
		}(i)
	}

	wg.Wait()
	return entries, nil
}

// getShardEntries 获取单个分片的条目
func (c *Cache) getShardEntries(shardIndex int) []CacheEntry {
	var entries []CacheEntry

	err := filepath.Walk(c.storagePath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() || strings.HasSuffix(path, ".tmp") || strings.HasSuffix(path, ".meta") {
			return nil
		}

		key := filepath.Base(path)
		if c.getShardIndex(key) != shardIndex {
			return nil
		}

		// 获取访问时间
		accessTime := info.ModTime()
		if stat, err := os.Stat(path); err == nil {
			accessTime = stat.ModTime()
		}

		entries = append(entries, CacheEntry{
			Key:        key,
			Path:       path,
			Size:       info.Size(),
			CreatedAt:  info.ModTime(),
			AccessedAt: accessTime,
		})

		return nil
	})

	if err != nil {
		c.logger.Error("Failed to walk shard %d: %v", shardIndex, err)
	}

	return entries
}

// getCacheEntries 获取所有缓存条目
func (c *Cache) getCacheEntries() ([]CacheEntry, error) {
	var entries []CacheEntry

	err := filepath.Walk(c.storagePath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() || strings.HasSuffix(path, ".tmp") {
			return nil
		}

		// 获取访问时间
		accessTime := info.ModTime()
		if stat, err := os.Stat(path); err == nil {
			accessTime = stat.ModTime()
		}

		entries = append(entries, CacheEntry{
			Key:        filepath.Base(path),
			Path:       path,
			Size:       info.Size(),
			CreatedAt:  info.ModTime(),
			AccessedAt: accessTime,
		})

		return nil
	})

	return entries, err
}

// updateStats 更新统计信息 (并发优化版本)
func (c *Cache) updateStats() error {
	if !c.config.Enabled {
		return nil
	}

	var wg sync.WaitGroup

	// 并发更新每个分片的统计信息
	for i := 0; i < c.shardCount; i++ {
		wg.Add(1)
		go func(shardIndex int) {
			defer wg.Done()
			c.updateShardStats(shardIndex)
		}(i)
	}

	wg.Wait()
	return nil
}

// updateShardStats 更新单个分片的统计信息
func (c *Cache) updateShardStats(shardIndex int) {
	var totalSize int64
	var fileCount int64
	shard := c.shards[shardIndex]

	err := filepath.Walk(c.storagePath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() || strings.HasSuffix(path, ".tmp") || strings.HasSuffix(path, ".meta") {
			return nil
		}

		key := filepath.Base(path)
		if c.getShardIndex(key) != shardIndex {
			return nil
		}

		totalSize += info.Size()
		fileCount++
		return nil
	})

	if err != nil {
		c.logger.Error("Failed to update shard %d stats: %v", shardIndex, err)
		return
	}

	// 原子更新分片统计
	atomic.StoreInt64(&shard.totalSize, totalSize)
	atomic.StoreInt64(&shard.fileCount, fileCount)
}

// startCleanupTask 启动清理任务
func (c *Cache) startCleanupTask(interval time.Duration) {
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			c.cleanup()
		case <-c.stopCleanup:
			return
		}
	}
}

// cleanup 清理过期文件 (并发优化版本)
func (c *Cache) cleanup() {
	if !c.config.Enabled {
		return
	}

	c.logger.Debug("Starting concurrent cache cleanup")

	var totalRemovedCount int64
	var totalRemovedSize int64
	var mu sync.Mutex
	var wg sync.WaitGroup

	// 为每个分片启动清理goroutine
	for i := 0; i < c.shardCount; i++ {
		wg.Add(1)
		go func(shardIndex int) {
			defer wg.Done()

			removedCount, removedSize := c.cleanupShard(shardIndex)

			mu.Lock()
			totalRemovedCount += removedCount
			totalRemovedSize += removedSize
			mu.Unlock()
		}(i)
	}

	wg.Wait()

	if totalRemovedCount > 0 {
		c.logger.Info("Cache cleanup completed: removed %d files (%d bytes)", totalRemovedCount, totalRemovedSize)
	}
}

// cleanupShard 清理单个分片的过期文件
func (c *Cache) cleanupShard(shardIndex int) (int64, int64) {
	var removedCount int64
	var removedSize int64
	shard := c.shards[shardIndex]

	// 收集需要删除的文件
	var filesToRemove []string
	var sizesToRemove []int64

	err := filepath.Walk(c.storagePath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() || strings.HasSuffix(path, ".tmp") {
			return nil
		}

		key := filepath.Base(path)
		if c.getShardIndex(key) != shardIndex {
			return nil
		}

		// 检查是否过期
		if time.Since(info.ModTime()) > c.expireTime {
			filesToRemove = append(filesToRemove, path)
			sizesToRemove = append(sizesToRemove, info.Size())
		}

		return nil
	})

	if err != nil {
		c.logger.Error("Failed to scan shard %d for cleanup: %v", shardIndex, err)
		return 0, 0
	}

	// 批量删除文件
	for i, path := range filesToRemove {
		if err := os.Remove(path); err != nil {
			c.logger.Warn("Failed to remove expired cache file: %v", err)
		} else {
			removedCount++
			removedSize += sizesToRemove[i]
		}
	}

	// 更新分片统计
	if removedCount > 0 {
		shard.mu.Lock()
		atomic.AddInt64(&shard.totalSize, -removedSize)
		atomic.AddInt64(&shard.fileCount, -removedCount)
		shard.mu.Unlock()
	}

	return removedCount, removedSize
}

// getTotalSize 获取总大小
func (c *Cache) getTotalSize() int64 {
	var total int64
	for _, shard := range c.shards {
		total += atomic.LoadInt64(&shard.totalSize)
	}
	return total
}

// getTotalFileCount 获取总文件数
func (c *Cache) getTotalFileCount() int64 {
	var total int64
	for _, shard := range c.shards {
		total += atomic.LoadInt64(&shard.fileCount)
	}
	return total
}

// GetStats 获取缓存统计 (无锁版本)
func (c *Cache) GetStats() CacheStats {
	return CacheStats{
		TotalSize:     c.getTotalSize(),
		FileCount:     c.getTotalFileCount(),
		HitCount:      atomic.LoadInt64(&c.hitCount),
		MissCount:     atomic.LoadInt64(&c.missCount),
		EvictionCount: atomic.LoadInt64(&c.evictionCount),
	}
}

// ensureSpaceAsync 异步确保空间
func (c *Cache) ensureSpaceAsync(needSize int64) error {
	go func() {
		c.ensureSpace(needSize)
	}()
	return nil
}

// Clear 清空缓存 (并发优化版本)
func (c *Cache) Clear() error {
	if !c.config.Enabled {
		return nil
	}

	var wg sync.WaitGroup

	// 并发清空每个分片
	for i := 0; i < c.shardCount; i++ {
		wg.Add(1)
		go func(shardIndex int) {
			defer wg.Done()
			c.clearShard(shardIndex)
		}(i)
	}

	wg.Wait()

	// 重置统计信息
	atomic.StoreInt64(&c.hitCount, 0)
	atomic.StoreInt64(&c.missCount, 0)
	atomic.StoreInt64(&c.evictionCount, 0)

	c.logger.Info("Cache cleared")
	return nil
}

// clearShard 清空单个分片
func (c *Cache) clearShard(shardIndex int) {
	shard := c.shards[shardIndex]

	err := filepath.Walk(c.storagePath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() {
			return nil
		}

		key := filepath.Base(path)
		if c.getShardIndex(key) != shardIndex {
			return nil
		}

		return os.Remove(path)
	})

	if err != nil {
		c.logger.Error("Failed to clear shard %d: %v", shardIndex, err)
	}

	// 重置分片统计
	shard.mu.Lock()
	atomic.StoreInt64(&shard.totalSize, 0)
	atomic.StoreInt64(&shard.fileCount, 0)
	shard.mu.Unlock()
}

// Close 关闭缓存
func (c *Cache) Close() {
	if c.stopCleanup != nil {
		select {
		case <-c.stopCleanup:
			// 通道已关闭
		default:
			close(c.stopCleanup)
		}
	}
}
