package server

import (
	"context"
	"io"
	"runtime"
	"sync"
	"time"
)

// ConcurrentManager 并发管理器
type ConcurrentManager struct {
	workerPool    *WorkerPool
	streamPool    *StreamPool
	bufferPool    *sync.Pool
	maxGoroutines int
}

// WorkerPool 工作池
type WorkerPool struct {
	workers   chan chan WorkItem
	workQueue chan WorkItem
	quit      chan bool
	wg        sync.WaitGroup
}

// WorkItem 工作项
type WorkItem struct {
	Task func()
}

// StreamPool 流处理池
type StreamPool struct {
	pool sync.Pool
}

// StreamBuffer 流缓冲区
type StreamBuffer struct {
	buf []byte
}

// NewConcurrentManager 创建并发管理器
func NewConcurrentManager() *ConcurrentManager {
	maxWorkers := runtime.NumCPU() * 4 // 4倍CPU核心数的工作协程
	maxGoroutines := runtime.NumCPU() * 100 // 最大协程数限制

	cm := &ConcurrentManager{
		workerPool:    NewWorkerPool(maxWorkers),
		streamPool:    NewStreamPool(),
		maxGoroutines: maxGoroutines,
		bufferPool: &sync.Pool{
			New: func() interface{} {
				return make([]byte, 64*1024) // 64KB缓冲区
			},
		},
	}

	return cm
}

// NewWorkerPool 创建工作池
func NewWorkerPool(maxWorkers int) *WorkerPool {
	// 限制最大工作协程数，避免资源耗尽
	if maxWorkers > 5 {
		maxWorkers = 5
	}

	pool := &WorkerPool{
		workers:   make(chan chan WorkItem, maxWorkers),
		workQueue: make(chan WorkItem, maxWorkers),
		quit:      make(chan bool),
	}

	// 启动工作协程
	for i := 0; i < maxWorkers; i++ {
		worker := NewWorker(pool.workers, pool.quit)
		worker.Start(&pool.wg)
		pool.wg.Add(1)
	}

	// 启动调度器
	go pool.dispatch()

	return pool
}

// Worker 工作协程
type Worker struct {
	workerPool chan chan WorkItem
	jobChannel chan WorkItem
	quit       chan bool
}

// NewWorker 创建工作协程
func NewWorker(workerPool chan chan WorkItem, quit chan bool) *Worker {
	return &Worker{
		workerPool: workerPool,
		jobChannel: make(chan WorkItem),
		quit:       quit,
	}
}

// Start 启动工作协程
func (w *Worker) Start(wg *sync.WaitGroup) {
	go func() {
		defer wg.Done()
		for {
			// 将工作通道注册到工作池
			select {
			case w.workerPool <- w.jobChannel:
				// 等待任务
				select {
				case job := <-w.jobChannel:
					// 执行任务
					job.Task()
				case <-w.quit:
					return
				}
			case <-w.quit:
				return
			}
		}
	}()
}

// dispatch 调度任务
func (wp *WorkerPool) dispatch() {
	for {
		select {
		case job, ok := <-wp.workQueue:
			if !ok {
				// 工作队列已关闭
				return
			}
			// 获取可用的工作协程
			go func(job WorkItem) {
				select {
				case worker := <-wp.workers:
					select {
					case worker <- job:
						// 任务分发成功
					case <-wp.quit:
						// 工作池已停止
						return
					}
				case <-wp.quit:
					// 工作池已停止
					return
				}
			}(job)
		case <-wp.quit:
			return
		}
	}
}

// Submit 提交任务
func (wp *WorkerPool) Submit(task func()) {
	work := WorkItem{Task: task}
	select {
	case wp.workQueue <- work:
		// 任务提交成功
	case <-wp.quit:
		// 工作池已停止，忽略任务
		return
	}
}

// Stop 停止工作池
func (wp *WorkerPool) Stop() {
	close(wp.quit)
	// 关闭工作队列，防止新任务提交
	close(wp.workQueue)
	wp.wg.Wait()
}

// NewStreamPool 创建流处理池
func NewStreamPool() *StreamPool {
	return &StreamPool{
		pool: sync.Pool{
			New: func() interface{} {
				return &StreamBuffer{
					buf: make([]byte, 128*1024), // 128KB缓冲区
				}
			},
		},
	}
}

// Get 获取流缓冲区
func (sp *StreamPool) Get() *StreamBuffer {
	return sp.pool.Get().(*StreamBuffer)
}

// Put 归还流缓冲区
func (sp *StreamPool) Put(sb *StreamBuffer) {
	sp.pool.Put(sb)
}

// CopyWithPool 使用池化缓冲区的高性能复制
func (cm *ConcurrentManager) CopyWithPool(ctx context.Context, dst io.Writer, src io.Reader) (int64, error) {
	buf := cm.bufferPool.Get().([]byte)
	defer cm.bufferPool.Put(buf)

	var written int64
	for {
		select {
		case <-ctx.Done():
			return written, ctx.Err()
		default:
		}

		nr, er := src.Read(buf)
		if nr > 0 {
			nw, ew := dst.Write(buf[0:nr])
			if nw < 0 || nr < nw {
				nw = 0
				if ew == nil {
					ew = io.ErrShortWrite
				}
			}
			written += int64(nw)
			if ew != nil {
				return written, ew
			}
			if nr != nw {
				return written, io.ErrShortWrite
			}
		}
		if er != nil {
			if er != io.EOF {
				return written, er
			}
			break
		}
	}
	return written, nil
}

// StreamCopy 流式复制，支持大文件
func (cm *ConcurrentManager) StreamCopy(ctx context.Context, dst io.Writer, src io.Reader, onProgress func(int64)) (int64, error) {
	streamBuf := cm.streamPool.Get()
	defer cm.streamPool.Put(streamBuf)

	var written int64
	lastReport := time.Now()

	for {
		select {
		case <-ctx.Done():
			return written, ctx.Err()
		default:
		}

		nr, er := src.Read(streamBuf.buf)
		if nr > 0 {
			nw, ew := dst.Write(streamBuf.buf[0:nr])
			if nw < 0 || nr < nw {
				nw = 0
				if ew == nil {
					ew = io.ErrShortWrite
				}
			}
			written += int64(nw)

			// 定期报告进度
			if onProgress != nil && time.Since(lastReport) > 100*time.Millisecond {
				onProgress(written)
				lastReport = time.Now()
			}

			if ew != nil {
				return written, ew
			}
			if nr != nw {
				return written, io.ErrShortWrite
			}
		}
		if er != nil {
			if er != io.EOF {
				return written, er
			}
			break
		}
	}

	if onProgress != nil {
		onProgress(written)
	}

	return written, nil
}

// AsyncTask 异步执行任务
func (cm *ConcurrentManager) AsyncTask(task func()) {
	cm.workerPool.Submit(task)
}

// ParallelProcess 并行处理多个任务
func (cm *ConcurrentManager) ParallelProcess(tasks []func()) {
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, cm.maxGoroutines)

	for _, task := range tasks {
		wg.Add(1)
		go func(t func()) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()
			t()
		}(task)
	}

	wg.Wait()
}

// BatchProcess 批量处理，控制并发数
func (cm *ConcurrentManager) BatchProcess(items []interface{}, processor func(interface{}), batchSize int) {
	semaphore := make(chan struct{}, batchSize)
	var wg sync.WaitGroup

	for _, item := range items {
		wg.Add(1)
		go func(i interface{}) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()
			processor(i)
		}(item)
	}

	wg.Wait()
}

// Stop 停止并发管理器
func (cm *ConcurrentManager) Stop() {
	cm.workerPool.Stop()
}
