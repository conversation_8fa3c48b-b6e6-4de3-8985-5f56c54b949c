package server

import (
	"context"
	"docker-lb/internal/auth"
	"docker-lb/internal/cache"
	"docker-lb/internal/config"
	"docker-lb/internal/database"
	"docker-lb/internal/loadbalancer"
	"docker-lb/internal/monitoring"
	"docker-lb/pkg/logger"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// Server HTTP服务器
type Server struct {
	config     *config.Config
	lb         *loadbalancer.LoadBalancer
	logger     *logger.Logger
	router     *gin.Engine
	server     *http.Server
	ws         *WebSocketHub
	db         *database.Database
	auth       *auth.AuthManager
	monitor    *monitoring.Monitor
	cache      *cache.Cache
	concurrent *ConcurrentManager
	stopCh     chan struct{} // 用于停止后台goroutine
}

// New 创建新的服务器
func New(cfg *config.Config, lb *loadbalancer.LoadBalancer, logger *logger.Logger, db *database.Database, cacheManager *cache.Cache) *Server {
	// 设置Gin模式
	if cfg.Logging.Level == "debug" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()
	
	// 添加中间件
	router.Use(gin.Recovery())
	router.Use(corsMiddleware())
	router.Use(loggingMiddleware(logger, cfg))

	// 添加缓存中间件（在Docker代理路由之前）
	if cacheManager != nil {
		router.Use(cacheManager.CacheMiddleware())
	}

	// 创建认证管理器
	authManager := auth.New(&cfg.Auth, logger)

	// 创建监控管理器
	monitor := monitoring.New(db, authManager, logger)

	// 创建并发管理器
	concurrentManager := NewConcurrentManager()

	s := &Server{
		config:     cfg,
		lb:         lb,
		logger:     logger,
		router:     router,
		ws:         NewWebSocketHub(logger),
		db:         db,
		auth:       authManager,
		monitor:    monitor,
		cache:      cacheManager,
		concurrent: concurrentManager,
		stopCh:     make(chan struct{}),
	}

	s.setupRoutes()

	s.server = &http.Server{
		Addr:    fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port),
		Handler: router,
	}

	return s
}

// setupRoutes 设置路由
func (s *Server) setupRoutes() {
	// 静态文件服务
	s.router.Static("/assets", "./web/dist/assets")
	s.router.StaticFile("/", "./web/dist/index.html")

	// API路由组
	api := s.router.Group("/api/v1")
	{
		// 公开接口 (只读，基础信息)
		api.GET("/health", s.getHealth)

		// 需要认证的路由
		protected := api.Group("/")
		protected.Use(s.auth.AuthMiddleware())
		{
			// 镜像源管理 (需要认证)
			protected.GET("/registries", s.getRegistries)
			protected.GET("/stats", s.getStats)
			protected.POST("/registries/:name/enable", s.enableRegistry)
			protected.POST("/registries/:name/disable", s.disableRegistry)

			// 镜像源配置管理 (需要认证)
			protected.GET("/registry-configs", s.getRegistryConfigs)
			protected.POST("/registry-configs", s.createRegistryConfig)
			protected.PUT("/registry-configs/:id", s.updateRegistryConfig)
			protected.DELETE("/registry-configs/:id", s.deleteRegistryConfig)

			// 缓存相关路由 (需要认证)
			if s.cache != nil {
				protected.GET("/cache/stats", s.cache.CacheStatsHandler())
				protected.POST("/cache/clear", s.cache.CacheClearHandler())
			}
		}
	}

	// 监控路由
	s.monitor.SetupRoutes(api)

	// WebSocket路由 (保留兼容性)
	s.router.GET("/ws", s.handleWebSocket)

	// Docker代理路由
	s.router.Any("/v2/*path", s.proxyDockerRequest)
}

// Start 启动服务器
func (s *Server) Start() error {
	// 启动WebSocket hub
	go s.ws.Run()

	// 启动监控数据推送
	go s.startMonitoring()

	return s.server.ListenAndServe()
}

// Shutdown 关闭服务器
func (s *Server) Shutdown(ctx context.Context) error {
	s.logger.Info("Starting graceful shutdown...")

	// 停止后台goroutine
	select {
	case <-s.stopCh:
		// 已经关闭
	default:
		close(s.stopCh)
	}

	// 停止健康检查器
	if s.lb != nil {
		s.lb.StopHealthCheck()
	}

	// 关闭WebSocket连接
	s.ws.Close()

	// 停止并发管理器
	if s.concurrent != nil {
		s.concurrent.Stop()
	}

	// 最后关闭HTTP服务器
	if err := s.server.Shutdown(ctx); err != nil {
		s.logger.Error("HTTP server shutdown error: %v", err)
		return err
	}

	// 关闭数据库和缓存
	if s.cache != nil {
		s.cache.Close()
	}
	if s.db != nil {
		s.db.Close()
	}

	s.logger.Info("Graceful shutdown completed")
	return nil
}

// getHealth 获取健康状态
func (s *Server) getHealth(c *gin.Context) {
	status := s.lb.GetRegistries()

	healthyCount := 0
	availableCount := 0
	disabledCount := 0
	totalCount := len(status)

	for _, reg := range status {
		reg.Mu.RLock()
		if reg.Healthy && !reg.Disabled && reg.Config.Enabled {
			availableCount++
		}
		if reg.Healthy {
			healthyCount++
		}
		if reg.Disabled {
			disabledCount++
		}
		reg.Mu.RUnlock()
	}

	c.JSON(http.StatusOK, gin.H{
		"status":              "ok",
		"timestamp":           time.Now().Format(time.RFC3339),
		"total_registries":    totalCount,
		"healthy_registries":  healthyCount,
		"available_registries": availableCount,
		"disabled_registries": disabledCount,
	})
}

// getRegistries 获取镜像源列表
func (s *Server) getRegistries(c *gin.Context) {
	registries := s.lb.GetRegistries()
	result := make([]map[string]interface{}, 0, len(registries))

	for _, reg := range registries {
		// 快速复制数据，减少锁持有时间
		reg.Mu.RLock()

		// 复制所有需要的数据
		name := reg.Config.Name
		url := reg.Config.URL
		weight := reg.Config.Weight
		enabled := reg.Config.Enabled
		healthy := reg.Healthy
		disabled := reg.Disabled
		totalRequests := reg.Stats.TotalRequests
		successRequests := reg.Stats.SuccessRequests
		failedRequests := reg.Stats.FailedRequests
		consecutiveFails := reg.Stats.ConsecutiveFails
		averageResponse := reg.Stats.AverageResponse
		lastHealthCheck := reg.Stats.LastHealthCheck
		lastReviveCheck := reg.LastReviveCheck

		reg.Mu.RUnlock() // 立即释放锁

		// 在锁外构建响应数据
		regInfo := map[string]interface{}{
			"name":              name,
			"url":               url,
			"weight":            weight,
			"enabled":           enabled,
			"healthy":           healthy,
			"disabled":          disabled,
			"available":         healthy && !disabled && enabled,
			"total_requests":    totalRequests,
			"success_requests":  successRequests,
			"failed_requests":   failedRequests,
			"consecutive_fails": consecutiveFails,
			"average_response":  averageResponse.Milliseconds(),
			"last_health_check": lastHealthCheck.Format(time.RFC3339),
			"last_revive_check": lastReviveCheck.Format(time.RFC3339),
		}
		result = append(result, regInfo)
	}

	c.JSON(http.StatusOK, gin.H{
		"registries": result,
	})
}

// getStats 获取统计信息
func (s *Server) getStats(c *gin.Context) {
	registries := s.lb.GetRegistries()

	var totalRequests, successRequests, failedRequests int64
	var totalResponseTime time.Duration
	healthyCount := 0

	for _, reg := range registries {
		// 快速复制数据，减少锁持有时间
		reg.Mu.RLock()
		regTotalRequests := reg.Stats.TotalRequests
		regSuccessRequests := reg.Stats.SuccessRequests
		regFailedRequests := reg.Stats.FailedRequests
		regAverageResponse := reg.Stats.AverageResponse
		regHealthy := reg.Healthy
		reg.Mu.RUnlock() // 立即释放锁

		// 在锁外进行计算
		totalRequests += regTotalRequests
		successRequests += regSuccessRequests
		failedRequests += regFailedRequests
		totalResponseTime += regAverageResponse
		if regHealthy {
			healthyCount++
		}
	}

	var avgResponseTime time.Duration
	if len(registries) > 0 {
		avgResponseTime = totalResponseTime / time.Duration(len(registries))
	}

	var successRate float64
	if totalRequests > 0 {
		successRate = float64(successRequests) / float64(totalRequests) * 100
	}

	c.JSON(http.StatusOK, gin.H{
		"total_requests":     totalRequests,
		"success_requests":   successRequests,
		"failed_requests":    failedRequests,
		"success_rate":       successRate,
		"average_response":   avgResponseTime.Milliseconds(),
		"healthy_registries": healthyCount,
		"total_registries":   len(registries),
		"timestamp":          time.Now().Format(time.RFC3339),
	})
}

// enableRegistry 启用镜像源
func (s *Server) enableRegistry(c *gin.Context) {
	name := c.Param("name")

	registries := s.lb.GetRegistries()
	found := false

	for _, reg := range registries {
		if reg.Config.Name == name {
			reg.Mu.Lock()
			reg.Config.Enabled = true
			reg.Mu.Unlock()
			found = true
			s.logger.Info("Registry %s has been enabled", name)
			break
		}
	}

	if !found {
		c.JSON(http.StatusNotFound, gin.H{"error": "Registry not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Registry enabled"})
}

// disableRegistry 禁用镜像源
func (s *Server) disableRegistry(c *gin.Context) {
	name := c.Param("name")

	registries := s.lb.GetRegistries()
	found := false

	for _, reg := range registries {
		if reg.Config.Name == name {
			reg.Mu.Lock()
			reg.Config.Enabled = false
			reg.Mu.Unlock()
			found = true
			s.logger.Info("Registry %s has been disabled", name)
			break
		}
	}

	if !found {
		c.JSON(http.StatusNotFound, gin.H{"error": "Registry not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Registry disabled"})
}

// getRegistryConfigs 获取所有镜像源配置
func (s *Server) getRegistryConfigs(c *gin.Context) {
	configs, err := s.db.GetAllRegistryConfigs()
	if err != nil {
		s.logger.Error("Failed to get registry configs: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get registry configs"})
		return
	}

	c.JSON(http.StatusOK, configs)
}

// createRegistryConfig 创建镜像源配置
func (s *Server) createRegistryConfig(c *gin.Context) {
	var config database.RegistryConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// 验证必填字段
	if config.Name == "" || config.URL == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Name and URL are required"})
		return
	}

	// 设置默认值
	if config.TimeoutSeconds <= 0 {
		config.TimeoutSeconds = 30
	}
	if config.Weight <= 0 {
		config.Weight = 1
	}

	// 检查名称是否已存在
	existing, err := s.db.GetRegistryConfigByName(config.Name)
	if err != nil {
		s.logger.Error("Failed to check existing registry: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check existing registry"})
		return
	}
	if existing != nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Registry name already exists"})
		return
	}

	// 创建配置
	if err := s.db.CreateRegistryConfig(&config); err != nil {
		s.logger.Error("Failed to create registry config: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create registry config"})
		return
	}

	// 刷新负载均衡器
	if err := s.lb.RefreshRegistries(); err != nil {
		s.logger.Error("Failed to refresh registries: %v", err)
		// 不返回错误，因为配置已经保存成功
	}

	s.logger.Info("Created registry config: %s", config.Name)
	c.JSON(http.StatusCreated, config)
}

// updateRegistryConfig 更新镜像源配置
func (s *Server) updateRegistryConfig(c *gin.Context) {
	idStr := c.Param("id")
	id := 0
	if _, err := fmt.Sscanf(idStr, "%d", &id); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid registry ID"})
		return
	}

	var config database.RegistryConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// 验证必填字段
	if config.Name == "" || config.URL == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Name and URL are required"})
		return
	}

	// 设置ID
	config.ID = id

	// 设置默认值
	if config.TimeoutSeconds <= 0 {
		config.TimeoutSeconds = 30
	}
	if config.Weight <= 0 {
		config.Weight = 1
	}

	// 检查配置是否存在
	existing, err := s.db.GetRegistryConfig(id)
	if err != nil {
		s.logger.Error("Failed to get registry config: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get registry config"})
		return
	}
	if existing == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Registry config not found"})
		return
	}

	// 如果名称改变，检查新名称是否已存在
	if existing.Name != config.Name {
		nameExists, err := s.db.GetRegistryConfigByName(config.Name)
		if err != nil {
			s.logger.Error("Failed to check registry name: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check registry name"})
			return
		}
		if nameExists != nil {
			c.JSON(http.StatusConflict, gin.H{"error": "Registry name already exists"})
			return
		}
	}

	// 更新配置
	if err := s.db.UpdateRegistryConfig(&config); err != nil {
		s.logger.Error("Failed to update registry config: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update registry config"})
		return
	}

	// 刷新负载均衡器
	if err := s.lb.RefreshRegistries(); err != nil {
		s.logger.Error("Failed to refresh registries: %v", err)
		// 不返回错误，因为配置已经更新成功
	}

	s.logger.Info("Updated registry config: %s", config.Name)
	c.JSON(http.StatusOK, config)
}

// deleteRegistryConfig 删除镜像源配置
func (s *Server) deleteRegistryConfig(c *gin.Context) {
	idStr := c.Param("id")
	id := 0
	if _, err := fmt.Sscanf(idStr, "%d", &id); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid registry ID"})
		return
	}

	// 检查配置是否存在
	existing, err := s.db.GetRegistryConfig(id)
	if err != nil {
		s.logger.Error("Failed to get registry config: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get registry config"})
		return
	}
	if existing == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Registry config not found"})
		return
	}

	// 删除配置
	if err := s.db.DeleteRegistryConfig(id); err != nil {
		s.logger.Error("Failed to delete registry config: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete registry config"})
		return
	}

	// 刷新负载均衡器
	if err := s.lb.RefreshRegistries(); err != nil {
		s.logger.Error("Failed to refresh registries: %v", err)
		// 不返回错误，因为配置已经删除成功
	}

	s.logger.Info("Deleted registry config: %s", existing.Name)
	c.JSON(http.StatusOK, gin.H{"message": "Registry config deleted successfully"})
}

// startMonitoring 启动监控数据推送
func (s *Server) startMonitoring() {
	ticker := time.NewTicker(60 * time.Second) // 改为60秒更新一次，进一步减少频率
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// 同步处理监控数据推送，避免goroutine累积
			func() {
				defer func() {
					if r := recover(); r != nil {
						s.logger.Error("Monitoring data push panic: %v", r)
					}
				}()

				if s.ws.HasClients() {
					stats := s.getStatsData()
					s.ws.Broadcast(stats)
				}
			}()
		case <-s.stopCh:
			s.logger.Info("Monitoring goroutine stopped")
			return
		}
	}
}

// getStatsData 获取统计数据
func (s *Server) getStatsData() map[string]interface{} {
	registries := s.lb.GetRegistries()
	result := make([]map[string]interface{}, 0, len(registries))

	for _, reg := range registries {
		// 快速复制数据，减少锁持有时间
		reg.Mu.RLock()
		name := reg.Config.Name
		enabled := reg.Config.Enabled
		healthy := reg.Healthy
		disabled := reg.Disabled
		totalRequests := reg.Stats.TotalRequests
		successRequests := reg.Stats.SuccessRequests
		failedRequests := reg.Stats.FailedRequests
		consecutiveFails := reg.Stats.ConsecutiveFails
		averageResponse := reg.Stats.AverageResponse
		lastHealthCheck := reg.Stats.LastHealthCheck
		lastReviveCheck := reg.LastReviveCheck
		reg.Mu.RUnlock() // 立即释放锁

		// 在锁外构建数据
		regInfo := map[string]interface{}{
			"name":              name,
			"healthy":           healthy,
			"disabled":          disabled,
			"available":         healthy && !disabled && enabled,
			"total_requests":    totalRequests,
			"success_requests":  successRequests,
			"failed_requests":   failedRequests,
			"consecutive_fails": consecutiveFails,
			"average_response":  averageResponse.Milliseconds(),
			"last_health_check": lastHealthCheck.Format(time.RFC3339),
			"last_revive_check": lastReviveCheck.Format(time.RFC3339),
		}
		result = append(result, regInfo)
	}

	return map[string]interface{}{
		"type":       "stats_update",
		"timestamp":  time.Now().Format(time.RFC3339),
		"registries": result,
	}
}

// corsMiddleware CORS中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// loggingMiddleware 日志中间件
func loggingMiddleware(logger *logger.Logger, cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		c.Next()

		latency := time.Since(start)
		clientIP := c.ClientIP()
		method := c.Request.Method
		statusCode := c.Writer.Status()

		if raw != "" {
			path = path + "?" + raw
		}

		// 跳过Docker代理请求的日志，因为在proxy.go中已经有详细日志
		if cfg.Logging.EnableRequestLog && !strings.HasPrefix(path, "/v2/") {
			userAgent := c.GetHeader("User-Agent")
			logger.Info("🌐 HTTP请求 - %s %s %d %v %s [%s]", method, path, statusCode, latency, clientIP, userAgent)
		} else if !strings.HasPrefix(path, "/v2/") {
			logger.Info("%s %s %d %v %s", method, path, statusCode, latency, clientIP)
		}
	}
}
