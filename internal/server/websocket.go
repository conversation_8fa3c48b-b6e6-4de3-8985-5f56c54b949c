package server

import (
	"docker-lb/pkg/logger"
	"encoding/json"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

const (
	// WebSocket超时设置
	writeWait      = 10 * time.Second    // 写入超时
	pongWait       = 60 * time.Second    // 等待pong消息超时
	pingPeriod     = (pongWait * 9) / 10 // ping消息发送间隔
	maxMessageSize = 512                 // 最大消息大小
)

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true // 允许所有来源
	},
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
}

// Client WebSocket客户端
type Client struct {
	conn   *websocket.Conn
	send   chan []byte
	hub    *WebSocketHub
	logger *logger.Logger
}

// WebSocketHub WebSocket集线器
type WebSocketHub struct {
	clients    map[*Client]bool
	broadcast  chan []byte
	register   chan *Client
	unregister chan *Client
	logger     *logger.Logger
	mu         sync.RWMutex
	done       chan struct{} // 用于停止hub
}

// NewWebSocketHub 创建新的WebSocket集线器
func NewWebSocketHub(logger *logger.Logger) *WebSocketHub {
	return &WebSocketHub{
		clients:    make(map[*Client]bool),
		broadcast:  make(chan []byte, 256),    // 增加广播缓冲区
		register:   make(chan *Client, 256),   // 增加注册缓冲区
		unregister: make(chan *Client, 256),   // 增加注销缓冲区
		logger:     logger,
		done:       make(chan struct{}),
	}
}

// Run 运行WebSocket集线器
func (h *WebSocketHub) Run() {
	for {
		select {
		case client := <-h.register:
			h.mu.Lock()
			h.clients[client] = true
			h.mu.Unlock()
			h.logger.Info("WebSocket client connected, total: %d", len(h.clients))

		case client := <-h.unregister:
			h.mu.Lock()
			if _, ok := h.clients[client]; ok {
				delete(h.clients, client)
				close(client.send)
			}
			h.mu.Unlock()
			h.logger.Info("WebSocket client disconnected, total: %d", len(h.clients))

		case message := <-h.broadcast:
			h.mu.Lock() // 使用写锁，因为可能需要删除客户端
			clientsToRemove := []*Client{}
			for client := range h.clients {
				select {
				case client.send <- message:
					// 消息发送成功
				default:
					// 客户端发送缓冲区满，标记为需要删除
					clientsToRemove = append(clientsToRemove, client)
				}
			}
			// 删除无响应的客户端
			for _, client := range clientsToRemove {
				delete(h.clients, client)
				close(client.send)
			}
			h.mu.Unlock()

		case <-h.done:
			h.logger.Info("WebSocket hub stopped")
			return
		}
	}
}

// Broadcast 广播消息
func (h *WebSocketHub) Broadcast(data interface{}) {
	message, err := json.Marshal(data)
	if err != nil {
		h.logger.Error("Failed to marshal broadcast data: %v", err)
		return
	}

	// 使用超时机制，避免阻塞
	select {
	case h.broadcast <- message:
		// 消息发送成功
	case <-time.After(1 * time.Second):
		h.logger.Warn("Broadcast timeout after 1 second, dropping message")
	default:
		h.logger.Warn("Broadcast channel is full, dropping message")
	}
}

// HasClients 检查是否有客户端连接
func (h *WebSocketHub) HasClients() bool {
	h.mu.RLock()
	defer h.mu.RUnlock()
	return len(h.clients) > 0
}

// Close 关闭WebSocket集线器
func (h *WebSocketHub) Close() {
	// 停止hub的运行循环
	select {
	case <-h.done:
		// 已经停止
	default:
		close(h.done)
	}

	h.mu.Lock()
	defer h.mu.Unlock()

	for client := range h.clients {
		client.conn.Close()
		delete(h.clients, client)
		close(client.send)
	}
}

// handleWebSocket 处理WebSocket连接
func (s *Server) handleWebSocket(c *gin.Context) {
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		s.logger.Error("Failed to upgrade WebSocket connection: %v", err)
		return
	}

	client := &Client{
		conn:   conn,
		send:   make(chan []byte, 1024), // 增加缓冲区大小
		hub:    s.ws,
		logger: s.logger,
	}

	// 注册客户端
	select {
	case client.hub.register <- client:
		// 注册成功，启动goroutines处理读写
		go client.writePump()
		go client.readPump()
	default:
		// 注册失败，关闭连接
		s.logger.Warn("Failed to register WebSocket client, hub may be full")
		conn.Close()
	}
}

// readPump 读取消息
func (c *Client) readPump() {
	defer func() {
		c.hub.unregister <- c
		c.conn.Close()
	}()

	// 设置读取限制和超时
	c.conn.SetReadLimit(maxMessageSize)
	c.conn.SetReadDeadline(time.Now().Add(pongWait))
	c.conn.SetPongHandler(func(string) error {
		c.conn.SetReadDeadline(time.Now().Add(pongWait))
		return nil
	})

	for {
		_, _, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				c.logger.Error("WebSocket error: %v", err)
			}
			break
		}
		// 重置读取超时
		c.conn.SetReadDeadline(time.Now().Add(pongWait))
	}
}

// writePump 写入消息
func (c *Client) writePump() {
	ticker := time.NewTicker(pingPeriod)
	defer func() {
		ticker.Stop()
		c.conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.send:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if !ok {
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			if err := c.conn.WriteMessage(websocket.TextMessage, message); err != nil {
				c.logger.Error("WebSocket write error: %v", err)
				return
			}

		case <-ticker.C:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				c.logger.Debug("WebSocket ping failed: %v", err)
				return
			}
		}
	}
}
