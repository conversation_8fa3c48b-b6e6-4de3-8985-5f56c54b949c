package server

import (
	"context"
	"docker-lb/internal/loadbalancer"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

// ConcurrentDownloader 并发下载器
type ConcurrentDownloader struct {
	server *Server
}

// DownloadResult 下载结果
type DownloadResult struct {
	Registry     *loadbalancer.Registry
	Response     *http.Response
	Error        error
	ResponseTime time.Duration
	Success      bool
}

// NewConcurrentDownloader 创建并发下载器
func NewConcurrentDownloader(server *Server) *ConcurrentDownloader {
	return &ConcurrentDownloader{
		server: server,
	}
}

// ConcurrentDownload 并发下载，返回最快的成功响应
func (cd *ConcurrentDownloader) ConcurrentDownload(c *gin.Context, registries []*loadbalancer.Registry, targetPath string) (*DownloadResult, error) {
	if len(registries) == 0 {
		return nil, fmt.Errorf("no available registries")
	}

	// 如果只有一个镜像源，直接使用原有逻辑
	if len(registries) == 1 {
		return cd.singleDownload(c, registries[0], targetPath)
	}

	// 多镜像源并发下载
	return cd.raceDownload(c, registries, targetPath)
}

// singleDownload 单镜像源下载
func (cd *ConcurrentDownloader) singleDownload(c *gin.Context, registry *loadbalancer.Registry, targetPath string) (*DownloadResult, error) {
	start := time.Now()
	
	targetURL, err := cd.server.buildTargetURL(registry.Config.URL, targetPath, c.Request.URL.RawQuery)
	if err != nil {
		return &DownloadResult{
			Registry:     registry,
			Error:        err,
			ResponseTime: time.Since(start),
			Success:      false,
		}, err
	}

	// 创建请求
	req, err := http.NewRequestWithContext(c.Request.Context(), c.Request.Method, targetURL, c.Request.Body)
	if err != nil {
		return &DownloadResult{
			Registry:     registry,
			Error:        err,
			ResponseTime: time.Since(start),
			Success:      false,
		}, err
	}

	// 复制请求头
	cd.server.copyHeaders(c.Request.Header, req.Header)

	// 执行请求
	client := &http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     90 * time.Second,
		},
	}

	resp, err := client.Do(req)
	responseTime := time.Since(start)
	success := err == nil && resp.StatusCode >= 200 && resp.StatusCode < 400

	return &DownloadResult{
		Registry:     registry,
		Response:     resp,
		Error:        err,
		ResponseTime: responseTime,
		Success:      success,
	}, err
}

// raceDownload 竞速下载，返回最快的成功响应
func (cd *ConcurrentDownloader) raceDownload(c *gin.Context, registries []*loadbalancer.Registry, targetPath string) (*DownloadResult, error) {
	ctx, cancel := context.WithCancel(c.Request.Context())
	defer cancel()

	resultChan := make(chan *DownloadResult, len(registries))
	var wg sync.WaitGroup

	// 启动所有镜像源的并发下载
	for _, registry := range registries {
		if !registry.IsHealthy() {
			continue // 跳过不健康的镜像源
		}

		wg.Add(1)
		go func(reg *loadbalancer.Registry) {
			defer wg.Done()
			
			// 为每个下载创建子上下文
			downloadCtx, downloadCancel := context.WithTimeout(ctx, 30*time.Second)
			defer downloadCancel()

			// 创建新的gin.Context副本用于并发下载
			downloadContext := cd.createDownloadContext(c, downloadCtx)
			
			result, _ := cd.singleDownload(downloadContext, reg, targetPath)

			// 发送结果到channel（非阻塞）
			select {
			case resultChan <- result:
				// 成功发送
			case <-ctx.Done():
				// 上下文已取消，清理资源
				if result.Response != nil {
					result.Response.Body.Close()
				}
			default:
				// channel满了，清理资源
				if result.Response != nil {
					result.Response.Body.Close()
				}
			}
		}(registry)
	}

	// 等待第一个成功的结果或所有下载完成
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	var bestResult *DownloadResult
	var firstError error
	successCount := 0
	totalCount := 0
	allResults := make([]*DownloadResult, 0, len(registries))

	for result := range resultChan {
		totalCount++
		allResults = append(allResults, result)

		if result.Success {
			successCount++
			if bestResult == nil || result.ResponseTime < bestResult.ResponseTime {
				bestResult = result
				// 找到第一个成功的结果后，取消其他下载
				cancel()
			}
		} else {
			if firstError == nil {
				firstError = result.Error
			}
		}
	}

	// 关闭除了最佳结果之外的所有响应体
	for _, result := range allResults {
		if result != bestResult && result.Response != nil {
			result.Response.Body.Close()
		}
	}

	if bestResult != nil {
		cd.server.logger.Info("🏆 并发下载成功 - 最快镜像源: %s, 响应时间: %v, 成功率: %d/%d", 
			bestResult.Registry.Config.Name, bestResult.ResponseTime, successCount, totalCount)
		return bestResult, nil
	}

	if firstError != nil {
		return nil, fmt.Errorf("all downloads failed, first error: %w", firstError)
	}

	return nil, fmt.Errorf("no successful downloads")
}

// createDownloadContext 为并发下载创建上下文副本
func (cd *ConcurrentDownloader) createDownloadContext(original *gin.Context, ctx context.Context) *gin.Context {
	// 创建新的请求副本
	req := original.Request.Clone(ctx)
	
	// 创建新的gin.Context
	newContext := &gin.Context{
		Request: req,
	}
	
	return newContext
}

// SupportsRangeRequests 检查是否支持断点续传
func (cd *ConcurrentDownloader) SupportsRangeRequests(c *gin.Context, registry *loadbalancer.Registry, targetPath string) bool {
	targetURL, err := cd.server.buildTargetURL(registry.Config.URL, targetPath, "")
	if err != nil {
		return false
	}

	// 发送HEAD请求检查Accept-Ranges头
	req, err := http.NewRequestWithContext(c.Request.Context(), "HEAD", targetURL, nil)
	if err != nil {
		return false
	}

	cd.server.copyHeaders(c.Request.Header, req.Header)

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	// 检查Accept-Ranges头
	acceptRanges := resp.Header.Get("Accept-Ranges")
	return acceptRanges == "bytes"
}

// ResumeDownload 断点续传下载
func (cd *ConcurrentDownloader) ResumeDownload(c *gin.Context, registry *loadbalancer.Registry, targetPath string, startByte int64) (*DownloadResult, error) {
	start := time.Now()
	
	targetURL, err := cd.server.buildTargetURL(registry.Config.URL, targetPath, c.Request.URL.RawQuery)
	if err != nil {
		return &DownloadResult{
			Registry:     registry,
			Error:        err,
			ResponseTime: time.Since(start),
			Success:      false,
		}, err
	}

	// 创建带Range头的请求
	req, err := http.NewRequestWithContext(c.Request.Context(), "GET", targetURL, nil)
	if err != nil {
		return &DownloadResult{
			Registry:     registry,
			Error:        err,
			ResponseTime: time.Since(start),
			Success:      false,
		}, err
	}

	// 复制原始请求头
	cd.server.copyHeaders(c.Request.Header, req.Header)
	
	// 设置Range头进行断点续传
	req.Header.Set("Range", fmt.Sprintf("bytes=%d-", startByte))

	client := &http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     90 * time.Second,
		},
	}

	resp, err := client.Do(req)
	responseTime := time.Since(start)
	
	// 206 Partial Content 表示断点续传成功
	success := err == nil && (resp.StatusCode == 206 || resp.StatusCode == 200)

	if success {
		cd.server.logger.Info("📥 断点续传成功 - 镜像源: %s, 起始字节: %d, 状态码: %d, 响应时间: %v", 
			registry.Config.Name, startByte, resp.StatusCode, responseTime)
	}

	return &DownloadResult{
		Registry:     registry,
		Response:     resp,
		Error:        err,
		ResponseTime: responseTime,
		Success:      success,
	}, err
}
