package server

import (
	"context"
	"docker-lb/internal/cache"
	"docker-lb/internal/database"
	"docker-lb/internal/loadbalancer"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// proxyDockerRequest 代理Docker请求
func (s *Server) proxyDockerRequest(c *gin.Context) {
	start := time.Now()
	clientIP := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	// 检查客户端是否已取消请求
	select {
	case <-c.Request.Context().Done():
		s.logger.Warn("🚫 客户端取消请求 - 路径: %s, 客户端: %s", c.Request.URL.Path, clientIP)
		return
	default:
	}

	// 记录请求开始日志
	if s.config.Logging.EnableRequestLog {
		s.logger.Info("🚀 Docker请求开始 - 方法: %s, 路径: %s, 客户端: %s, User-Agent: %s",
			c.Request.Method, c.Request.URL.Path, clientIP, userAgent)
	}

	// 检查是否支持断点续传
	rangeHeader := c.GetHeader("Range")
	isRangeRequest := rangeHeader != ""

	// 首先检查缓存（在选择镜像源之前）
	if s.cache != nil && !isRangeRequest {
		if cachedResult := s.checkCacheForAllRegistries(c); cachedResult != nil {
			// 缓存命中，直接返回
			s.handleCacheHit(c, cachedResult, start, clientIP, userAgent)
			return
		}
	}

	// 对于大文件（blob）且有多个镜像源时，使用并发下载
	useConcurrentDownload := strings.Contains(c.Request.URL.Path, "/blobs/") &&
		!isRangeRequest && // 断点续传请求不使用并发下载
		s.lb.GetRegistryCount() > 1

	var registry *loadbalancer.Registry
	var err error

	if useConcurrentDownload {
		// 使用并发下载器
		downloader := NewConcurrentDownloader(s)
		healthyRegistries := s.lb.GetHealthyRegistries()

		if s.config.Logging.EnableRequestLog {
			s.logger.Info("🚀 启动并发下载 - 可用镜像源: %d, 路径: %s (缓存未命中)", len(healthyRegistries), c.Request.URL.Path)
		}

		result, err := downloader.ConcurrentDownload(c, healthyRegistries, c.Request.URL.Path)
		if err != nil {
			s.logger.Error("❌ 并发下载失败: %v", err)
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"error": "Concurrent download failed",
			})
			return
		}

		// 处理并发下载的结果
		s.handleConcurrentDownloadResult(c, result, start, clientIP, userAgent)
		return
	} else {
		// 获取单一镜像源
		registry, err = s.lb.GetNextRegistry()
		if err != nil {
			s.logger.Error("❌ 无可用镜像源: %v", err)
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"error": "No available registry",
			})
			return
		}
	}

	// 构建目标URL
	targetURL, err := s.buildTargetURL(registry.Config.URL, c.Request.URL.Path, c.Request.URL.RawQuery)
	if err != nil {
		s.logger.Error("❌ 构建目标URL失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to build target URL",
		})
		return
	}

	// 设置目标URL到上下文，供缓存中间件使用
	c.Set("target_url", targetURL)

	// 记录选择的镜像源
	if s.config.Logging.EnableRequestLog {
		s.logger.Info("🎯 选择镜像源: %s (缓存未命中), 目标URL: %s", registry.Config.Name, targetURL)
	}

	// 创建带上下文的代理请求
	proxyReq, err := http.NewRequestWithContext(c.Request.Context(), c.Request.Method, targetURL, c.Request.Body)
	if err != nil {
		s.logger.Error("❌ 创建代理请求失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create proxy request",
		})
		return
	}

	// 复制请求头
	s.copyHeaders(c.Request.Header, proxyReq.Header)
	
	// 设置超时
	client := &http.Client{
		Timeout: registry.Config.Timeout,
	}

	// 执行请求
	resp, err := client.Do(proxyReq)
	responseTime := time.Since(start)

	if err != nil {
		// 检查是否是上下文取消导致的错误
		if c.Request.Context().Err() != nil {
			s.logger.Warn("🚫 客户端取消请求导致代理失败 - 镜像源: %s, 响应时间: %v", registry.Config.Name, responseTime)
			return
		}

		s.logger.Error("❌ 代理请求失败 - 镜像源: %s, 错误: %v, 响应时间: %v", registry.Config.Name, err, responseTime)
		// 更新统计信息（排除健康检查请求）
		if !s.isHealthCheckRequest(userAgent, c.Request.URL.Path) {
			s.lb.UpdateRegistryStats(registry, false, responseTime)
		}

		// 尝试重试其他镜像源
		if s.config.LoadBalancer.MaxRetries > 0 {
			if s.config.Logging.EnableRequestLog {
				s.logger.Info("🔄 准备重试其他镜像源...")
			}
			s.retryRequest(c, 1)
			return
		}

		c.JSON(http.StatusBadGateway, gin.H{
			"error": "Proxy request failed",
		})
		return
	}
	defer resp.Body.Close()

	// 更新统计信息（排除健康检查请求）
	success := resp.StatusCode < 400
	if !s.isHealthCheckRequest(userAgent, c.Request.URL.Path) {
		s.lb.UpdateRegistryStats(registry, success, responseTime)
	}

	// 复制响应头
	s.copyHeaders(resp.Header, c.Writer.Header())
	c.Header("X-Cache", "MISS")
	c.Header("X-Cache-Registry", registry.Config.Name)

	// 对于404错误，添加友好的错误信息头
	if resp.StatusCode == 404 {
		c.Header("X-Docker-Error", "Image not found in registry")
		c.Header("X-Docker-Suggestion", "Please check image name and tag")
	}

	c.Status(resp.StatusCode)

	// 复制响应体并计算字节数，同时缓存
	var bytesOut int64
	if s.cache != nil && s.shouldCacheResponse(c.Request.URL.Path, resp) {
		// 读取完整响应体到内存，使用带上下文的读取
		bodyBytes, err := s.readAllWithContext(c.Request.Context(), resp.Body)
		if err != nil {
			if c.Request.Context().Err() != nil {
				s.logger.Warn("🚫 客户端在读取响应时取消请求: %v", err)
				return
			}
			s.logger.Error("❌ 读取响应体失败: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to read response"})
			return
		}

		// 异步缓存响应，使用工作池
		s.concurrent.AsyncTask(func() {
			if cacheErr := s.cache.SetWithResponse(targetURL, c.Request.URL.Path, resp, bodyBytes); cacheErr != nil {
				s.logger.Warn("Failed to cache response: %v", cacheErr)
			} else {
				s.logger.Debug("Cached response: %s%s (%d bytes)", targetURL, c.Request.URL.Path, len(bodyBytes))
			}
		})

		// 写入响应，检查客户端连接
		bytesOut = int64(len(bodyBytes))
		if err := s.writeWithContext(c.Request.Context(), c.Writer, bodyBytes); err != nil {
			if c.Request.Context().Err() != nil {
				s.logger.Warn("🚫 客户端在写入响应时取消请求: %v", err)
				return
			}
			s.logger.Error("❌ 写入响应失败: %v", err)
		}
	} else {
		// 使用高性能流式复制，支持进度报告
		bytesOut, err = s.concurrent.StreamCopy(c.Request.Context(), c.Writer, resp.Body, func(written int64) {
			// 可选：报告传输进度
			if written%1024*1024 == 0 { // 每MB报告一次
				s.logger.Debug("Transferred %d bytes", written)
			}
		})
		if err != nil {
			if c.Request.Context().Err() != nil {
				s.logger.Warn("🚫 客户端在复制响应时取消请求: %v", err)
				return
			}
			s.logger.Error("❌ 复制响应体失败: %v", err)
		}
	}

	// 记录请求完成日志
	statusIcon := "✅"
	logMessage := ""
	if !success {
		statusIcon = "⚠️"
		if resp.StatusCode == 404 {
			logMessage = " (镜像不存在)"
		} else if resp.StatusCode >= 500 {
			logMessage = " (服务器错误)"
		} else if resp.StatusCode >= 400 {
			logMessage = " (客户端错误)"
		}
	}

	if s.config.Logging.EnableRequestLog {
		s.logger.Info("%s Docker请求完成 - 镜像源: %s, 方法: %s, 路径: %s, 状态码: %d, 响应时间: %v, 客户端: %s%s",
			statusIcon, registry.Config.Name, c.Request.Method, c.Request.URL.Path, resp.StatusCode, responseTime, clientIP, logMessage)
	} else {
		s.logger.Debug("Proxied request to %s: %s %s -> %d (%v)",
			registry.Config.Name, c.Request.Method, c.Request.URL.Path, resp.StatusCode, responseTime)
	}

	// 异步记录到数据库，使用工作池（排除健康检查请求）
	if s.db != nil && !s.isHealthCheckRequest(userAgent, c.Request.URL.Path) {
		requestLog := &database.RequestLog{
			Timestamp:    start,
			ClientIP:     clientIP,
			Method:       c.Request.Method,
			Path:         c.Request.URL.Path,
			UserAgent:    userAgent,
			Registry:     registry.Config.Name,
			StatusCode:   resp.StatusCode,
			ResponseTime: responseTime.Milliseconds(),
			BytesIn:      c.Request.ContentLength,
			BytesOut:     bytesOut,
			Success:      success,
		}

		s.concurrent.AsyncTask(func() {
			if err := s.db.LogRequest(requestLog); err != nil {
				s.logger.Error("Failed to log request to database: %v", err)
			}
		})
	}
}

// retryRequest 重试请求
func (s *Server) retryRequest(c *gin.Context, attempt int) {
	if attempt > s.config.LoadBalancer.MaxRetries {
		c.JSON(http.StatusBadGateway, gin.H{
			"error": "All registries failed",
		})
		return
	}

	// 等待重试延迟
	time.Sleep(s.config.LoadBalancer.RetryDelay)

	// 获取下一个镜像源
	registry, err := s.lb.GetNextRegistry()
	if err != nil {
		s.logger.Error("❌ 重试时无可用镜像源 (第%d次尝试): %v", attempt, err)
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "No available registry",
		})
		return
	}

	s.logger.Info("🔄 重试请求 - 镜像源: %s (第%d次尝试)", registry.Config.Name, attempt)

	// 执行单次代理请求，避免递归调用
	s.executeSingleProxyRequest(c, registry, attempt)
}

// executeSingleProxyRequest 执行单次代理请求，避免递归调用
func (s *Server) executeSingleProxyRequest(c *gin.Context, registry *loadbalancer.Registry, attempt int) {
	start := time.Now()
	clientIP := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	// 检查客户端是否已取消请求
	select {
	case <-c.Request.Context().Done():
		s.logger.Warn("🚫 客户端取消重试请求 - 路径: %s, 客户端: %s", c.Request.URL.Path, clientIP)
		return
	default:
	}

	// 构建目标URL
	targetURL, err := s.buildTargetURL(registry.Config.URL, c.Request.URL.Path, c.Request.URL.RawQuery)
	if err != nil {
		s.logger.Error("❌ 重试时构建目标URL失败: %v", err)
		if attempt < s.config.LoadBalancer.MaxRetries {
			s.retryRequest(c, attempt+1)
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to build target URL"})
		}
		return
	}

	// 创建带上下文的代理请求
	proxyReq, err := http.NewRequestWithContext(c.Request.Context(), c.Request.Method, targetURL, c.Request.Body)
	if err != nil {
		s.logger.Error("❌ 重试时创建代理请求失败: %v", err)
		if attempt < s.config.LoadBalancer.MaxRetries {
			s.retryRequest(c, attempt+1)
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create proxy request"})
		}
		return
	}

	// 复制请求头
	s.copyHeaders(c.Request.Header, proxyReq.Header)

	// 设置超时
	client := &http.Client{
		Timeout: registry.Config.Timeout,
	}

	// 执行请求
	resp, err := client.Do(proxyReq)
	responseTime := time.Since(start)

	if err != nil {
		// 检查是否是上下文取消导致的错误
		if c.Request.Context().Err() != nil {
			s.logger.Warn("🚫 客户端取消重试请求导致代理失败 - 镜像源: %s, 响应时间: %v", registry.Config.Name, responseTime)
			return
		}

		s.logger.Error("❌ 重试代理请求失败 - 镜像源: %s, 错误: %v, 响应时间: %v", registry.Config.Name, err, responseTime)
		// 更新统计信息
		if !s.isHealthCheckRequest(userAgent, c.Request.URL.Path) {
			s.lb.UpdateRegistryStats(registry, false, responseTime)
		}

		// 继续重试
		if attempt < s.config.LoadBalancer.MaxRetries {
			s.retryRequest(c, attempt+1)
		} else {
			c.JSON(http.StatusBadGateway, gin.H{"error": "All registries failed"})
		}
		return
	}
	defer resp.Body.Close()

	// 更新统计信息
	success := resp.StatusCode < 400
	if !s.isHealthCheckRequest(userAgent, c.Request.URL.Path) {
		s.lb.UpdateRegistryStats(registry, success, responseTime)
	}

	// 复制响应头
	s.copyHeaders(resp.Header, c.Writer.Header())
	c.Header("X-Cache", "MISS")
	c.Header("X-Cache-Registry", registry.Config.Name)
	c.Header("X-Retry-Attempt", fmt.Sprintf("%d", attempt))

	// 对于404错误，添加友好的错误信息头
	if resp.StatusCode == 404 {
		c.Header("X-Docker-Error", "Image not found in registry")
		c.Header("X-Docker-Suggestion", "Please check image name and tag")
	}

	c.Status(resp.StatusCode)

	// 复制响应体
	var bytesOut int64
	bytesOut, err = s.concurrent.StreamCopy(c.Request.Context(), c.Writer, resp.Body, nil)
	if err != nil {
		if c.Request.Context().Err() != nil {
			s.logger.Warn("🚫 客户端在重试复制响应时取消请求: %v", err)
			return
		}
		s.logger.Error("❌ 重试复制响应体失败: %v", err)
	}

	// 记录请求完成日志
	statusIcon := "✅"
	if !success {
		statusIcon = "⚠️"
	}

	if s.config.Logging.EnableRequestLog {
		s.logger.Info("%s 重试请求完成 - 镜像源: %s, 方法: %s, 路径: %s, 状态码: %d, 响应时间: %v, 尝试次数: %d, 客户端: %s",
			statusIcon, registry.Config.Name, c.Request.Method, c.Request.URL.Path, resp.StatusCode, responseTime, attempt, clientIP)
	}

	// 异步记录到数据库
	if s.db != nil && !s.isHealthCheckRequest(userAgent, c.Request.URL.Path) {
		requestLog := &database.RequestLog{
			Timestamp:    start,
			ClientIP:     clientIP,
			Method:       c.Request.Method,
			Path:         c.Request.URL.Path,
			UserAgent:    userAgent,
			Registry:     registry.Config.Name,
			StatusCode:   resp.StatusCode,
			ResponseTime: responseTime.Milliseconds(),
			BytesIn:      c.Request.ContentLength,
			BytesOut:     bytesOut,
			Success:      success,
		}

		s.concurrent.AsyncTask(func() {
			if err := s.db.LogRequest(requestLog); err != nil {
				s.logger.Error("Failed to log retry request to database: %v", err)
			}
		})
	}
}

// buildTargetURL 构建目标URL
func (s *Server) buildTargetURL(baseURL, path, query string) (string, error) {
	base, err := url.Parse(baseURL)
	if err != nil {
		return "", err
	}

	target := base.ResolveReference(&url.URL{
		Path:     path,
		RawQuery: query,
	})

	return target.String(), nil
}

// copyHeaders 复制HTTP头
func (s *Server) copyHeaders(src, dst http.Header) {
	for key, values := range src {
		// 跳过一些不应该转发的头
		if s.shouldSkipHeader(key) {
			continue
		}
		
		for _, value := range values {
			dst.Add(key, value)
		}
	}
}

// shouldSkipHeader 判断是否应该跳过某个头
func (s *Server) shouldSkipHeader(header string) bool {
	skipHeaders := []string{
		"Connection",
		"Keep-Alive",
		"Proxy-Authenticate",
		"Proxy-Authorization",
		"Te",
		"Trailers",
		"Transfer-Encoding",
		"Upgrade",
	}

	header = strings.ToLower(header)
	for _, skip := range skipHeaders {
		if strings.ToLower(skip) == header {
			return true
		}
	}
	return false
}

// shouldCacheResponse 判断是否应该缓存响应
func (s *Server) shouldCacheResponse(path string, resp *http.Response) bool {
	if s.cache == nil {
		return false
	}

	// 只缓存成功的响应
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return false
	}

	// 检查内容类型
	contentType := resp.Header.Get("Content-Type")
	if contentType != "" && !strings.Contains(contentType, "application/") && !strings.Contains(contentType, "octet-stream") {
		return false
	}

	// 检查内容长度限制
	if resp.ContentLength > 0 {
		maxFileSize, _ := parseSize(s.config.Cache.MaxFileSize)
		if resp.ContentLength > maxFileSize {
			return false
		}
	}

	// 检查路径类型
	if strings.Contains(path, "/manifests/") || strings.Contains(path, "/blobs/") {
		return true
	}

	return false
}

// parseSize 解析大小字符串 (简化版本)
func parseSize(sizeStr string) (int64, error) {
	switch {
	case strings.HasSuffix(sizeStr, "GB"):
		val := strings.TrimSuffix(sizeStr, "GB")
		if num, err := strconv.ParseFloat(val, 64); err == nil {
			return int64(num * 1024 * 1024 * 1024), nil
		}
	case strings.HasSuffix(sizeStr, "MB"):
		val := strings.TrimSuffix(sizeStr, "MB")
		if num, err := strconv.ParseFloat(val, 64); err == nil {
			return int64(num * 1024 * 1024), nil
		}
	}
	return 1024*1024*1024, nil // 默认1GB
}

// handleConcurrentDownloadResult 处理并发下载结果
func (s *Server) handleConcurrentDownloadResult(c *gin.Context, result *DownloadResult, start time.Time, clientIP, userAgent string) {
	defer result.Response.Body.Close()

	// 设置响应头
	s.copyHeaders(result.Response.Header, c.Writer.Header())
	c.Header("X-Cache", "MISS")
	c.Header("X-Cache-Registry", result.Registry.Config.Name)
	c.Header("X-Concurrent-Download", "true")

	// 对于404错误，添加友好的错误信息头
	if result.Response.StatusCode == 404 {
		c.Header("X-Docker-Error", "Image not found in registry")
		c.Header("X-Docker-Suggestion", "Please check image name and tag")
	}

	c.Status(result.Response.StatusCode)

	// 复制响应体
	var bytesOut int64
	var err error

	if s.cache != nil && s.shouldCacheResponse(c.Request.URL.Path, result.Response) {
		// 读取并缓存响应
		bodyBytes, err := s.readAllWithContext(c.Request.Context(), result.Response.Body)
		if err != nil {
			if c.Request.Context().Err() != nil {
				s.logger.Warn("🚫 客户端在并发下载时取消请求: %v", err)
				return
			}
			s.logger.Error("❌ 读取并发下载响应失败: %v", err)
			return
		}

		// 异步缓存
		targetURL, _ := s.buildTargetURL(result.Registry.Config.URL, c.Request.URL.Path, c.Request.URL.RawQuery)
		s.concurrent.AsyncTask(func() {
			if cacheErr := s.cache.SetWithResponse(targetURL, c.Request.URL.Path, result.Response, bodyBytes); cacheErr != nil {
				s.logger.Warn("Failed to cache concurrent download: %v", cacheErr)
			}
		})

		// 写入响应
		bytesOut = int64(len(bodyBytes))
		if err := s.writeWithContext(c.Request.Context(), c.Writer, bodyBytes); err != nil {
			if c.Request.Context().Err() != nil {
				s.logger.Warn("🚫 客户端在写入并发下载响应时取消请求: %v", err)
				return
			}
			s.logger.Error("❌ 写入并发下载响应失败: %v", err)
		}
	} else {
		// 直接流式传输
		bytesOut, err = s.concurrent.StreamCopy(c.Request.Context(), c.Writer, result.Response.Body, func(written int64) {
			if written%1024*1024 == 0 {
				s.logger.Debug("Concurrent download progress: %d bytes", written)
			}
		})
		if err != nil {
			if c.Request.Context().Err() != nil {
				s.logger.Warn("🚫 客户端在并发下载传输时取消请求: %v", err)
				return
			}
			s.logger.Error("❌ 并发下载传输失败: %v", err)
		}
	}

	// 记录成功日志
	responseTime := time.Since(start)
	success := result.Response.StatusCode < 400

	statusIcon := "✅"
	if !success {
		statusIcon = "⚠️"
	}

	if s.config.Logging.EnableRequestLog {
		s.logger.Info("%s 并发下载完成 - 最快镜像源: %s, 方法: %s, 路径: %s, 状态码: %d, 响应时间: %v, 传输: %d bytes, 客户端: %s",
			statusIcon, result.Registry.Config.Name, c.Request.Method, c.Request.URL.Path,
			result.Response.StatusCode, responseTime, bytesOut, clientIP)
	}

	// 更新统计（排除健康检查请求）
	if !s.isHealthCheckRequest(userAgent, c.Request.URL.Path) {
		s.lb.UpdateRegistryStats(result.Registry, success, responseTime)
	}

	// 异步记录到数据库（排除健康检查请求）
	if s.db != nil && !s.isHealthCheckRequest(userAgent, c.Request.URL.Path) {
		requestLog := &database.RequestLog{
			Timestamp:    start,
			ClientIP:     clientIP,
			Method:       c.Request.Method,
			Path:         c.Request.URL.Path,
			UserAgent:    userAgent,
			Registry:     result.Registry.Config.Name,
			StatusCode:   result.Response.StatusCode,
			ResponseTime: responseTime.Milliseconds(),
			BytesIn:      c.Request.ContentLength,
			BytesOut:     bytesOut,
			Success:      success,
		}

		s.concurrent.AsyncTask(func() {
			if err := s.db.LogRequest(requestLog); err != nil {
				s.logger.Error("Failed to log concurrent download to database: %v", err)
			}
		})
	}
}

// extractDigestFromPath 从路径中提取digest
func extractDigestFromPath(path string) string {
	// 从路径如 /v2/library/ubuntu/manifests/sha256:xxx 中提取 sha256:xxx
	parts := strings.Split(path, "/")
	if len(parts) > 0 {
		lastPart := parts[len(parts)-1]
		if strings.HasPrefix(lastPart, "sha256:") {
			return lastPart
		}
	}
	return ""
}

// copyWithContext 带上下文的数据复制
func (s *Server) copyWithContext(ctx context.Context, dst io.Writer, src io.Reader) (int64, error) {
	buf := make([]byte, 32*1024) // 32KB buffer
	var written int64

	for {
		select {
		case <-ctx.Done():
			return written, ctx.Err()
		default:
		}

		nr, er := src.Read(buf)
		if nr > 0 {
			nw, ew := dst.Write(buf[0:nr])
			if nw < 0 || nr < nw {
				nw = 0
				if ew == nil {
					ew = fmt.Errorf("invalid write result")
				}
			}
			written += int64(nw)
			if ew != nil {
				return written, ew
			}
			if nr != nw {
				return written, io.ErrShortWrite
			}
		}
		if er != nil {
			if er != io.EOF {
				return written, er
			}
			break
		}
	}
	return written, nil
}

// readAllWithContext 带上下文的完整读取
func (s *Server) readAllWithContext(ctx context.Context, r io.Reader) ([]byte, error) {
	var buf []byte
	chunk := make([]byte, 32*1024)

	for {
		select {
		case <-ctx.Done():
			return buf, ctx.Err()
		default:
		}

		n, err := r.Read(chunk)
		if n > 0 {
			buf = append(buf, chunk[:n]...)
		}
		if err != nil {
			if err == io.EOF {
				return buf, nil
			}
			return buf, err
		}
	}
}

// writeWithContext 带上下文的数据写入
func (s *Server) writeWithContext(ctx context.Context, w io.Writer, data []byte) error {
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
	}

	_, err := w.Write(data)
	return err
}

// CacheResult 缓存检查结果
type CacheResult struct {
	Response *cache.CachedResponse
	Reader   io.ReadCloser
	Registry *loadbalancer.Registry
}

// checkCacheForAllRegistries 检查所有镜像源的缓存
func (s *Server) checkCacheForAllRegistries(c *gin.Context) *CacheResult {
	registries := s.lb.GetRegistries()

	for _, registry := range registries {
		// 构建目标URL
		targetURL, err := s.buildTargetURL(registry.Config.URL, c.Request.URL.Path, c.Request.URL.RawQuery)
		if err != nil {
			continue
		}

		// 检查缓存
		if cachedResp, reader, found := s.cache.GetWithResponse(targetURL, c.Request.URL.Path); found {
			return &CacheResult{
				Response: cachedResp,
				Reader:   reader,
				Registry: registry,
			}
		}
	}

	return nil
}

// handleCacheHit 处理缓存命中
func (s *Server) handleCacheHit(c *gin.Context, result *CacheResult, start time.Time, clientIP, userAgent string) {
	defer result.Reader.Close()

	// 检查客户端是否已取消请求
	select {
	case <-c.Request.Context().Done():
		s.logger.Warn("🚫 客户端在缓存命中时取消请求 - 路径: %s, 客户端: %s", c.Request.URL.Path, clientIP)
		return
	default:
	}

	if s.config.Logging.EnableRequestLog {
		s.logger.Info("💾 缓存命中 - 路径: %s, 原始来源: %s, 响应时间: %v",
			c.Request.URL.Path, result.Registry.Config.Name, time.Since(start))
	}

	// 应用缓存的响应头
	result.Response.ApplyHeaders(c.Writer.Header())

	// 设置缓存相关头
	c.Header("X-Cache", "HIT")
	c.Header("X-Cache-Registry", result.Registry.Config.Name)

	// 设置状态码
	c.Status(result.Response.StatusCode)

	// 如果是HEAD请求，只返回头部
	if c.Request.Method == "HEAD" {
		// 缓存命中的HEAD请求，使用固定的快速响应时间（不使用实际传输时间）
		if !s.isHealthCheckRequest(userAgent, c.Request.URL.Path) {
			s.lb.UpdateRegistryStats(result.Registry, true, 1*time.Millisecond) // 缓存命中使用固定的1ms响应时间
		}
		return
	}

	// 返回缓存内容，使用高性能并发复制
	if _, err := s.concurrent.CopyWithPool(c.Request.Context(), c.Writer, result.Reader); err != nil {
		if c.Request.Context().Err() != nil {
			s.logger.Warn("🚫 客户端在缓存传输时取消请求: %v", err)
		} else {
			s.logger.Error("❌ 缓存内容复制失败: %v", err)
		}
		return
	}

	// 更新统计（缓存命中也算成功请求，排除健康检查请求）
	// 缓存命中使用固定的快速响应时间，不使用实际传输时间
	if !s.isHealthCheckRequest(userAgent, c.Request.URL.Path) {
		s.lb.UpdateRegistryStats(result.Registry, true, 1*time.Millisecond) // 缓存命中使用固定的1ms响应时间
	}

	// 记录实际传输时间用于日志
	responseTime := time.Since(start)

	// 记录缓存命中完成日志
	if s.config.Logging.EnableRequestLog {
		s.logger.Info("✅ 缓存请求完成 - 路径: %s, 原始来源: %s, 响应时间: %v, 客户端: %s",
			c.Request.URL.Path, result.Registry.Config.Name, responseTime, clientIP)
	}
}

// isHealthCheckRequest 判断是否是健康检查请求
func (s *Server) isHealthCheckRequest(userAgent, path string) bool {
	// 检查User-Agent是否是健康检查器
	if strings.Contains(userAgent, "Docker-LB-HealthChecker") {
		return true
	}

	// 检查路径是否是健康检查端点
	if strings.HasSuffix(path, "/v2/") && strings.Contains(userAgent, "Go-http-client") {
		return true
	}

	// 检查是否是内部健康检查请求
	if strings.Contains(userAgent, "HealthCheck") || strings.Contains(userAgent, "health-check") {
		return true
	}

	return false
}
