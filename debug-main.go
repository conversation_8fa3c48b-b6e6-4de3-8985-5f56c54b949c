package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
)

func main() {
	fmt.Println("Starting minimal debug server...")

	// 创建最简单的Gin服务器
	gin.SetMode(gin.ReleaseMode)
	router := gin.New()
	
	// 添加基本的中间件
	router.Use(gin.Recovery())
	
	// 添加基本的健康检查接口
	router.GET("/api/v1/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status": "ok",
			"time":   time.Now().Format(time.RFC3339),
		})
	})
	
	// 添加基本的统计接口
	router.GET("/api/v1/stats", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"requests": 0,
			"uptime":   time.Since(time.Now()).String(),
		})
	})

	// 创建HTTP服务器
	server := &http.Server{
		Addr:         ":60010",
		Handler:      router,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	// 启动服务器
	go func() {
		fmt.Println("Server starting on :60010")
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Printf("Server error: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	fmt.Println("Shutting down server...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Printf("Server forced to shutdown: %v", err)
	}

	fmt.Println("Server exited")
}
