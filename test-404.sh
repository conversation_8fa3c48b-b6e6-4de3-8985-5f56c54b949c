#!/bin/bash

echo "=== 测试Docker负载均衡器404错误处理 ==="

# 启动负载均衡器（后台运行）
echo "启动负载均衡器..."
./bin/docker-lb -config config-test-404.yaml &
LB_PID=$!

# 等待服务启动
echo "等待服务启动..."
sleep 3

# 测试404错误
echo ""
echo "=== 测试不存在的镜像 ==="
echo "请求: GET /v2/library/nonexistent-image/manifests/latest"

# 使用curl测试404响应
curl -v -H "Accept: application/vnd.docker.distribution.manifest.v2+json" \
     "http://localhost:60010/v2/library/nonexistent-image/manifests/latest" \
     2>&1 | grep -E "(HTTP|X-Docker|< )"

echo ""
echo "=== 测试另一个不存在的镜像 ==="
echo "请求: GET /v2/library/php7/manifests/latest"

curl -v -H "Accept: application/vnd.docker.distribution.manifest.v2+json" \
     "http://localhost:60010/v2/library/php7/manifests/latest" \
     2>&1 | grep -E "(HTTP|X-Docker|< )"

echo ""
echo "=== 检查日志 ==="
sleep 1

# 停止负载均衡器
echo ""
echo "停止负载均衡器..."
kill $LB_PID 2>/dev/null

echo "测试完成！"
